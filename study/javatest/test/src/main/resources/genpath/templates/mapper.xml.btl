<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper}.${table.mapperName}">

<% if(enableCache){ %>
    <!-- 开启二级缓存 -->
    <cache type="${cacheClassName}"/>

<% } %>
<% if(baseResultMap){ %>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="${package.Entity}.${entity}">
<% for(field in table.fields){ %>
   <% /** 生成主键排在第一位 **/ %>
   <% if(field.keyFlag){ %>
        <id column="${field.name}" property="${field.propertyName}" />
   <% } %>
<% } %>
<% for(field in table.commonFields){ %>
    <% /** 生成公共字段 **/ %>
        <result column="${field.name}" property="${field.propertyName}" />
<% } %>
<% for(field in table.fields){ %>
   <% /** 生成普通字段 **/ %>
   <% if(!field.keyFlag){ %>
        <result column="${field.name}" property="${field.propertyName}" />
   <% } %>
<% } %>
    </resultMap>
<% } %>
<% if(baseColumnList){ %>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
<% for(field in table.commonFields){ %>
        ${field.columnName},
<% } %>
        ${table.fieldNames}
    </sql>

<% } %>
</mapper>
