package

import org.springframework.stereotype.Service;

${package.ServiceImpl}
        ;
    ${package.Entity}.${entity};
    ${package.Mapper}.${table.mapperName};
    ${package.Service}.${table.serviceName};
    ${superServiceImplClassPackage}
        ;

/**
 * <p>
 * $!{table.comment} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
#if(${kotlin})
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
#else
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

}
#end
