<#include "public_include.ftl"/>
<@is_have_date_field table />
<@idJavaFieldName table />
package ${package.Controller};



import com.fmb.basic.FmbRespBean;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.server2022.config.filter.RequiresPermissions;

import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;


<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>

/**
 * <p>
 * ${table.comment!} 前端控制器
 * </p>
 *
 * <AUTHOR>
 */



// 实体名字:    ${entity}
// 实体名字2 :  ${table.entityPath}
// 表名称:     ${table.name}
// 接口名字 :   ${table.serviceName}
// 接口实现类:  ${table.serviceImplName}
@RestController
@RequestMapping("/admin/${table.entityPath}")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} {
</#if>


    @Autowired
    I${entity}Service ${table.entityPath}Service ;


    @PostMapping(value = "/${table.entityPath}List")
    @RequiresPermissions("${table.entityPath}:list")
    public FmbRespBean ${table.entityPath}List(@RequestBody JSONObject par) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        LambdaQueryWrapper<${entity}> wraper = new LambdaQueryWrapper<>(${entity}.class)
//                .eq(StringUtils.isNotBlank(par.getString("")),  ${entity}::get,par.getString(""))
                ;
        Integer reqPageNo = par.getInteger("pageno");
        Integer pagesize = par.getInteger("pagesize");
        //构造分页参数
        IPage<${entity}> page = new Page<>(reqPageNo ==null?1: reqPageNo, pagesize ==null?10: pagesize);
        //查询得到结果
        IPage<${entity}> pageInfo = ${table.entityPath}Service.page(page,wraper);

        resultMap.put("listsData",pageInfo.getRecords()) ;
        resultMap.put("total",pageInfo.getTotal()) ;
        resultMap.put("pagesize",pageInfo.getSize()) ;
        resultMap.put("pageno",pageInfo.getCurrent()) ;


        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/${table.entityPath}Add")
    @RequiresPermissions("${table.entityPath}:add")
    @ResponseBody
    public FmbRespBean ${table.entityPath}Add(@RequestBody ${entity} input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        boolean save = ${table.entityPath}Service.save(input);
        resultMap.put("saveResult", save);
        if (save) {
            resultMap.put("${priJavaField!}", input.get${priJavaFieldCap!}());
        }

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/singleAdd${table.entityPath}")
    @ResponseBody
    public FmbRespBean singleAdd() {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        ${entity} doamin = new ${entity}();

        boolean save = ${table.entityPath}Service.save(doamin);
        resultMap.put("saveResult", save);
        if (save) {
            resultMap.put("${priJavaField!}", doamin.get${priJavaFieldCap!}());

            resultMap.put("domain", doamin);
        }

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/${table.entityPath}Update")
    @RequiresPermissions("${table.entityPath}:update")
    @ResponseBody
    public FmbRespBean ${table.entityPath}Update(@RequestBody ${entity} input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        checkInput(input);

        resultMap.put("updateResult",${table.entityPath}Service.updateById(input) );

        return FmbRespBean.success(resultMap);
    }

    private void checkInput(@RequestBody ${entity} input) {
        if (input.get${priJavaFieldCap!}() == null) {
            throw new BadLogicException("${table.entityPath}主键为空");
        }
        if(${table.entityPath}Service.getById(input.get${priJavaFieldCap!}())==null){
            throw new BadLogicException("${table.entityPath}实体不存在");
        }
    }

    @RequestMapping(value = "/${table.entityPath}Del")
    @RequiresPermissions("${table.entityPath}:del")
    @ResponseBody
    public FmbRespBean ${table.entityPath}Del(@RequestBody ${entity} input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        checkInput(input);
        resultMap.put("delResult",${table.entityPath}Service.removeById(input.get${priJavaFieldCap!}()));

        return FmbRespBean.success(resultMap);
    }


}
</#if>
