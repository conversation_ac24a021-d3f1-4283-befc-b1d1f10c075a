package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 兑换码记录表，电子票
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_exchange_codes")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbExchangeCodes implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code_id", type = IdType.AUTO)
    private Integer codeId;

    /**
     * 统一兑换码
     */
    private String exchangeCode;

    /**
     * 兑换密码
     */
    private String exchangePwd;

    /**
     * 第三方二维码
     */
    private String qrCode;

    /**
     * 第三方
     */
    private String thirdPart;

    /**
     * 对应的商家ID
     */
    private Integer shopUserId;

    /**
     * 核销商家ID
     */
    private Integer verifyShopUserId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 单独商品订购ID
     */
    private Integer recId;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime useTime;

    /**
     * 后台操作人 -1 前台用户, -2 系统
     */
    private Integer adminUid;

    /**
     * 0未使用,1已使用,2无效,3使用中
     */
    private Integer status;

    /**
     * 预约码代表剩余次数
     */
    private Integer totalNum;

    /**
     * 码使用张数
     */
    private Integer usedNum;

    /**
     * 码已退张数
     */
    private Integer cancelNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getCodeId() {
        return codeId;
    }

    public void setCodeId(Integer codeId) {
        this.codeId = codeId;
    }
    public String getExchangeCode() {
        return exchangeCode;
    }

    public void setExchangeCode(String exchangeCode) {
        this.exchangeCode = exchangeCode;
    }
    public String getExchangePwd() {
        return exchangePwd;
    }

    public void setExchangePwd(String exchangePwd) {
        this.exchangePwd = exchangePwd;
    }
    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
    public String getThirdPart() {
        return thirdPart;
    }

    public void setThirdPart(String thirdPart) {
        this.thirdPart = thirdPart;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getVerifyShopUserId() {
        return verifyShopUserId;
    }

    public void setVerifyShopUserId(Integer verifyShopUserId) {
        this.verifyShopUserId = verifyShopUserId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }
    public Integer getUsedNum() {
        return usedNum;
    }

    public void setUsedNum(Integer usedNum) {
        this.usedNum = usedNum;
    }
    public Integer getCancelNum() {
        return cancelNum;
    }

    public void setCancelNum(Integer cancelNum) {
        this.cancelNum = cancelNum;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbExchangeCodes{" +
            "codeId=" + codeId +
            ", exchangeCode=" + exchangeCode +
            ", exchangePwd=" + exchangePwd +
            ", qrCode=" + qrCode +
            ", thirdPart=" + thirdPart +
            ", shopUserId=" + shopUserId +
            ", verifyShopUserId=" + verifyShopUserId +
            ", orderSn=" + orderSn +
            ", recId=" + recId +
            ", useTime=" + useTime +
            ", adminUid=" + adminUid +
            ", status=" + status +
            ", totalNum=" + totalNum +
            ", usedNum=" + usedNum +
            ", cancelNum=" + cancelNum +
            ", ctime=" + ctime +
        "}";
    }
}
