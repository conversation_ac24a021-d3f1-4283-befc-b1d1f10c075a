package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbMycpsProduct;
import com.fmb.server2022.fmbx.mapper.FmbMycpsProductMapper;
import com.fmb.server2022.fmbx.service.IFmbMycpsProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 分销商品信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
public class FmbMycpsProductServiceImpl extends ServiceImpl<FmbMycpsProductMapper, FmbMycpsProduct> implements IFmbMycpsProductService {

}
