package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 微信推文链接表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_cpssrc")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbCpssrc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微信推文链接ID
     */
    @TableId(value = "cpssrc_id", type = IdType.AUTO)
    private Integer cpssrcId;

    /**
     * cpssrc值
     */
    private String cpssrcValue;

    /**
     * 类型(扩展字段):1-微信文章,2-社区产品链接
     */
    private Integer cpssrcType;

    /**
     * 关联活动ID
     */
    private Integer aid;

    /**
     * 后台用户id
     */
    private Integer adminUid;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 公众号id, 1-北京订阅号,2-上海订阅号,3-服务号,4-北京服务号,5-上海服务号
     */
    private Integer wxMpId;

    /**
     * 推文日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate publishDate;

    /**
     * 推文排序值
     */
    private Integer sortValue;

    /**
     * 访问量
     */
    private Integer viewCount;

    /**
     * 微信小程序链接地址
     */
    private String wxAppUrl;

    /**
     * h5链接地址
     */
    private String h5Url;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 是否删除,0-未删,1-删除
     */
    private Integer isDelete;

    public Integer getCpssrcId() {
        return cpssrcId;
    }

    public void setCpssrcId(Integer cpssrcId) {
        this.cpssrcId = cpssrcId;
    }
    public String getCpssrcValue() {
        return cpssrcValue;
    }

    public void setCpssrcValue(String cpssrcValue) {
        this.cpssrcValue = cpssrcValue;
    }
    public Integer getCpssrcType() {
        return cpssrcType;
    }

    public void setCpssrcType(Integer cpssrcType) {
        this.cpssrcType = cpssrcType;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public Integer getWxMpId() {
        return wxMpId;
    }

    public void setWxMpId(Integer wxMpId) {
        this.wxMpId = wxMpId;
    }
    public LocalDate getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(LocalDate publishDate) {
        this.publishDate = publishDate;
    }
    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }
    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    public String getWxAppUrl() {
        return wxAppUrl;
    }

    public void setWxAppUrl(String wxAppUrl) {
        this.wxAppUrl = wxAppUrl;
    }
    public String geth5Url() {
        return h5Url;
    }

    public void seth5Url(String h5Url) {
        this.h5Url = h5Url;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "FmbCpssrc{" +
            "cpssrcId=" + cpssrcId +
            ", cpssrcValue=" + cpssrcValue +
            ", cpssrcType=" + cpssrcType +
            ", aid=" + aid +
            ", adminUid=" + adminUid +
            ", title=" + title +
            ", wxMpId=" + wxMpId +
            ", publishDate=" + publishDate +
            ", sortValue=" + sortValue +
            ", viewCount=" + viewCount +
            ", wxAppUrl=" + wxAppUrl +
            ", h5Url=" + h5Url +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", isDelete=" + isDelete +
        "}";
    }
}
