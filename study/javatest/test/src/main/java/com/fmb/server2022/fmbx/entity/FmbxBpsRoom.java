package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 商户房型
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bps_room")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpsRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "room_id", type = IdType.AUTO)
    private Integer roomId;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 房型说明
     */
    private String roomIntroduce;

    /**
     * 房间总数
     */
    private Integer roomNum;

    /**
     * 楼层
     */
    private String floorNumber;

    /**
     * 建筑面积
     */
    private String buildArea;

    /**
     * 床型数据JSON
     */
    private String bedType;

    /**
     * 加床:1-未知,2-不可加床,3-免费加不含早,4-免费加含早,5-收费加床不含早,6-收费加床含早
     */
    private Integer addBedType;

    /**
     * 加床费用说明
     */
    private String addBedFeeInfo;

    /**
     * 最大入住成人数
     */
    private Integer maxPeopleNum;

    /**
     * 吸烟信息:1-可吸烟,2-禁烟,3-部分客房可吸烟
     */
    private Integer smokeType;

    /**
     * 有无浴缸:0-没有,1-有
     */
    private Integer bathtubType;

    /**
     * 是否启用:0-屏蔽,1-启用
     */
    private Integer roomStatus;

    /**
     * 台后操作用户
     */
    private Integer adminUid;

    /**
     * 后台用户类型:1-父母邦后台,2-供应商后台
     */
    private Integer adminType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;


    private String roomImportKey ;

    public String getRoomImportKey() {
        return roomImportKey;
    }

    public void setRoomImportKey(String roomImportKey) {
        this.roomImportKey = roomImportKey;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }
    public String getRoomIntroduce() {
        return roomIntroduce;
    }

    public void setRoomIntroduce(String roomIntroduce) {
        this.roomIntroduce = roomIntroduce;
    }
    public Integer getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(Integer roomNum) {
        this.roomNum = roomNum;
    }
    public String getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(String floorNumber) {
        this.floorNumber = floorNumber;
    }

    public String getBuildArea() {
        return buildArea;
    }

    public void setBuildArea(String buildArea) {
        this.buildArea = buildArea;
    }

    public String getBedType() {
        return bedType;
    }

    public void setBedType(String bedType) {
        this.bedType = bedType;
    }
    public Integer getAddBedType() {
        return addBedType;
    }

    public void setAddBedType(Integer addBedType) {
        this.addBedType = addBedType;
    }

    public String getAddBedFeeInfo() {
        return addBedFeeInfo;
    }

    public void setAddBedFeeInfo(String addBedFeeInfo) {
        this.addBedFeeInfo = addBedFeeInfo;
    }

    public Integer getMaxPeopleNum() {
        return maxPeopleNum;
    }

    public void setMaxPeopleNum(Integer maxPeopleNum) {
        this.maxPeopleNum = maxPeopleNum;
    }
    public Integer getSmokeType() {
        return smokeType;
    }

    public void setSmokeType(Integer smokeType) {
        this.smokeType = smokeType;
    }
    public Integer getBathtubType() {
        return bathtubType;
    }

    public void setBathtubType(Integer bathtubType) {
        this.bathtubType = bathtubType;
    }
    public Integer getRoomStatus() {
        return roomStatus;
    }

    public void setRoomStatus(Integer roomStatus) {
        this.roomStatus = roomStatus;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxBpsRoom{" +
            "roomId=" + roomId +
            ", bpsId=" + bpsId +
            ", roomName=" + roomName +
            ", roomIntroduce=" + roomIntroduce +
            ", roomNum=" + roomNum +
            ", floorNumber=" + floorNumber +
            ", buildArea=" + buildArea +
            ", bedType=" + bedType +
            ", addBedType=" + addBedType +
            ", addBedFeeInfo=" + addBedFeeInfo +
            ", maxPeopleNum=" + maxPeopleNum +
            ", smokeType=" + smokeType +
            ", bathtubType=" + bathtubType +
            ", roomStatus=" + roomStatus +
            ", adminUid=" + adminUid +
            ", adminType=" + adminType +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", roomImportKey=" + roomImportKey +
        "}";
    }
}
