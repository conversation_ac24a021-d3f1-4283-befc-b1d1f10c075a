package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderReturns;
import com.fmb.server2022.fmbx.mapper.FmbOrderReturnsMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单退货记录列表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
public class FmbOrderReturnsServiceImpl extends ServiceImpl<FmbOrderReturnsMapper, FmbOrderReturns> implements IFmbOrderReturnsService {

}
