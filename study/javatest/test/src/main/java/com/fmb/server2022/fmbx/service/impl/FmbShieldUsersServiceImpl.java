package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbShieldUsers;
import com.fmb.server2022.fmbx.mapper.FmbShieldUsersMapper;
import com.fmb.server2022.fmbx.service.IFmbShieldUsersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 屏蔽用户表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-09
 */
@Service
public class FmbShieldUsersServiceImpl extends ServiceImpl<FmbShieldUsersMapper, FmbShieldUsers> implements IFmbShieldUsersService {

}
