package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.mapper.FmbxHotelReserveSkuMapper;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 房券sku表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-12-20
 */
@Service
public class FmbxHotelReserveSkuServiceImpl extends ServiceImpl<FmbxHotelReserveSkuMapper, FmbxHotelReserveSku> implements IFmbxHotelReserveSkuService {

}
