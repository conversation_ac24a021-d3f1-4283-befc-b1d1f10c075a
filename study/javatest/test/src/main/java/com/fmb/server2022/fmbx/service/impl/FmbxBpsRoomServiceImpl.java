package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.mapper.FmbxBpsRoomMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 商户房型 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-26
 */
@Service
public class FmbxBpsRoomServiceImpl extends ServiceImpl<FmbxBpsRoomMapper, FmbxBpsRoom> implements IFmbxBpsRoomService {

}
