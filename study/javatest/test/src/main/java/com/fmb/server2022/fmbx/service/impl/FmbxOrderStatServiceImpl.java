package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxOrderStat;
import com.fmb.server2022.fmbx.mapper.FmbxOrderStatMapper;
import com.fmb.server2022.fmbx.service.IFmbxOrderStatService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * fmbx活动主表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-06
 */
@Service
public class FmbxOrderStatServiceImpl extends ServiceImpl<FmbxOrderStatMapper, FmbxOrderStat> implements IFmbxOrderStatService {

}
