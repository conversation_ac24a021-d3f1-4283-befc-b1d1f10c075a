package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbUserCashLog;
import com.fmb.server2022.fmbx.mapper.FmbUserCashLogMapper;
import com.fmb.server2022.fmbx.service.IFmbUserCashLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 用户账户余额流水日志 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class FmbUserCashLogServiceImpl extends ServiceImpl<FmbUserCashLogMapper, FmbUserCashLog> implements IFmbUserCashLogService {

}
