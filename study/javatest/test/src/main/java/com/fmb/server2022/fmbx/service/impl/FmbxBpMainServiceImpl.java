package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.mapper.FmbxBpMainMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商主表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
public class FmbxBpMainServiceImpl extends ServiceImpl<FmbxBpMainMapper, FmbxBpMain> implements IFmbxBpMainService {

}
