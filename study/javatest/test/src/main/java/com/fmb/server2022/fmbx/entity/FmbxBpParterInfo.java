package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商商家信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_parter_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpParterInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bp_id")
    private Integer bpId;

    /**
     * 商家名称
     */
    private String partnerName;

    /**
     * 联系人
     */
    private String partnerContactsName;

    /**
     * 联系电话
     */
    private String partnerContactsPhone;

    /**
     * 联系手机号
     */
    private String partnerContactsMobile;

    /**
     * 邮箱
     */
    private String partnerContactsEmail;

    /**
     * 省份
     */
    private String partnerProvince;

    /**
     * 市
     */
    private String partnerCity;

    /**
     * 地址
     */
    private String partnerAddress;

    /**
     * 结算账户名字
     */
    private String settleAccountName;

    /**
     * 结算账户账号
     */
    private String settleAccountNo;

    /**
     * 结算账户开户行
     */
    private String settleAccountBank;

    /**
     * 结算财务联系人
     */
    private String settleAccountUsername;

    /**
     * 结算财务联系人电话
     */
    private String settleAccountUserMobile;

    /**
     * 结算财务联系人邮箱
     */
    private String settleAccountUserEmail;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate expireDate;

    //永久有效
    private Integer noend ;

    public Integer getNoend() {
        return noend;
    }

    public void setNoend(Integer noend) {
        this.noend = noend;
    }

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
    public String getPartnerContactsName() {
        return partnerContactsName;
    }

    public void setPartnerContactsName(String partnerContactsName) {
        this.partnerContactsName = partnerContactsName;
    }
    public String getPartnerContactsPhone() {
        return partnerContactsPhone;
    }

    public void setPartnerContactsPhone(String partnerContactsPhone) {
        this.partnerContactsPhone = partnerContactsPhone;
    }
    public String getPartnerContactsMobile() {
        return partnerContactsMobile;
    }

    public void setPartnerContactsMobile(String partnerContactsMobile) {
        this.partnerContactsMobile = partnerContactsMobile;
    }
    public String getPartnerContactsEmail() {
        return partnerContactsEmail;
    }

    public void setPartnerContactsEmail(String partnerContactsEmail) {
        this.partnerContactsEmail = partnerContactsEmail;
    }
    public String getPartnerProvince() {
        return partnerProvince;
    }

    public void setPartnerProvince(String partnerProvince) {
        this.partnerProvince = partnerProvince;
    }
    public String getPartnerCity() {
        return partnerCity;
    }

    public void setPartnerCity(String partnerCity) {
        this.partnerCity = partnerCity;
    }
    public String getPartnerAddress() {
        return partnerAddress;
    }

    public void setPartnerAddress(String partnerAddress) {
        this.partnerAddress = partnerAddress;
    }
    public String getSettleAccountName() {
        return settleAccountName;
    }

    public void setSettleAccountName(String settleAccountName) {
        this.settleAccountName = settleAccountName;
    }
    public String getSettleAccountNo() {
        return settleAccountNo;
    }

    public void setSettleAccountNo(String settleAccountNo) {
        this.settleAccountNo = settleAccountNo;
    }
    public String getSettleAccountBank() {
        return settleAccountBank;
    }

    public void setSettleAccountBank(String settleAccountBank) {
        this.settleAccountBank = settleAccountBank;
    }
    public String getSettleAccountUsername() {
        return settleAccountUsername;
    }

    public void setSettleAccountUsername(String settleAccountUsername) {
        this.settleAccountUsername = settleAccountUsername;
    }
    public String getSettleAccountUserMobile() {
        return settleAccountUserMobile;
    }

    public void setSettleAccountUserMobile(String settleAccountUserMobile) {
        this.settleAccountUserMobile = settleAccountUserMobile;
    }
    public String getSettleAccountUserEmail() {
        return settleAccountUserEmail;
    }

    public void setSettleAccountUserEmail(String settleAccountUserEmail) {
        this.settleAccountUserEmail = settleAccountUserEmail;
    }
    public LocalDate getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(LocalDate expireDate) {
        this.expireDate = expireDate;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxBpParterInfo{" +
            "bpId=" + bpId +
            ", partnerName=" + partnerName +
            ", partnerContactsName=" + partnerContactsName +
            ", partnerContactsPhone=" + partnerContactsPhone +
            ", partnerContactsMobile=" + partnerContactsMobile +
            ", partnerContactsEmail=" + partnerContactsEmail +
            ", partnerProvince=" + partnerProvince +
            ", partnerCity=" + partnerCity +
            ", partnerAddress=" + partnerAddress +
            ", settleAccountName=" + settleAccountName +
            ", settleAccountNo=" + settleAccountNo +
            ", settleAccountBank=" + settleAccountBank +
            ", settleAccountUsername=" + settleAccountUsername +
            ", settleAccountUserMobile=" + settleAccountUserMobile +
            ", settleAccountUserEmail=" + settleAccountUserEmail +
            ", expireDate=" + expireDate +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
