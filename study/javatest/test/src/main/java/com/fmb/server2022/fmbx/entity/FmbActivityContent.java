package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 活动更多信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_activity_content")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbActivityContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 活动的ID
     */
    private Integer aid;

    /**
     * 其他问题
     */
    private String otherState;

    /**
     * 活动摘要及规则
     */
    private String summary;

    /**
     * 活动常见问题
     */
    private String faq;

    /**
     * 活动详情内容
     */
    private String content;

    /**
     * 手机活动详情内容
     */
    private String appContent;

    /**
     * 手机活动详情专供APP使用内容
     */
    private String appContentNew;

    /**
     * 票务政策
     */
    private String policyDesc;

    /**
     * 报名规则配置
     */
    private String applyConfig;

    /**
     * 报名活动是否需要身份证:0:不需要 1 需要
     */
    private Integer applyIsCert;

    /**
     * 报名活动身份证数量
     */
    private Integer applyCertNum;

    /**
     * 是否为新老数据 0 老数据 ，1 新数据
     */
    private Integer isOldData;

    /**
     * 视频封面图
     */
    private String coverImg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public String getOtherState() {
        return otherState;
    }

    public void setOtherState(String otherState) {
        this.otherState = otherState;
    }
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
    public String getFaq() {
        return faq;
    }

    public void setFaq(String faq) {
        this.faq = faq;
    }
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public String getAppContent() {
        return appContent;
    }

    public void setAppContent(String appContent) {
        this.appContent = appContent;
    }
    public String getAppContentNew() {
        return appContentNew;
    }

    public void setAppContentNew(String appContentNew) {
        this.appContentNew = appContentNew;
    }
    public String getPolicyDesc() {
        return policyDesc;
    }

    public void setPolicyDesc(String policyDesc) {
        this.policyDesc = policyDesc;
    }
    public String getApplyConfig() {
        return applyConfig;
    }

    public void setApplyConfig(String applyConfig) {
        this.applyConfig = applyConfig;
    }
    public Integer getApplyIsCert() {
        return applyIsCert;
    }

    public void setApplyIsCert(Integer applyIsCert) {
        this.applyIsCert = applyIsCert;
    }
    public Integer getApplyCertNum() {
        return applyCertNum;
    }

    public void setApplyCertNum(Integer applyCertNum) {
        this.applyCertNum = applyCertNum;
    }
    public Integer getIsOldData() {
        return isOldData;
    }

    public void setIsOldData(Integer isOldData) {
        this.isOldData = isOldData;
    }
    public String getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(String coverImg) {
        this.coverImg = coverImg;
    }

    @Override
    public String toString() {
        return "FmbActivityContent{" +
            "id=" + id +
            ", aid=" + aid +
            ", otherState=" + otherState +
            ", summary=" + summary +
            ", faq=" + faq +
            ", content=" + content +
            ", appContent=" + appContent +
            ", appContentNew=" + appContentNew +
            ", policyDesc=" + policyDesc +
            ", applyConfig=" + applyConfig +
            ", applyIsCert=" + applyIsCert +
            ", applyCertNum=" + applyCertNum +
            ", isOldData=" + isOldData +
            ", coverImg=" + coverImg +
        "}";
    }
}
