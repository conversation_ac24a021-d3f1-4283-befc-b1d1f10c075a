package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxActivityLike;
import com.fmb.server2022.fmbx.mapper.FmbxActivityLikeMapper;
import com.fmb.server2022.fmbx.service.IFmbxActivityLikeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * fmbx活动收藏表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-03
 */
@Service
public class FmbxActivityLikeServiceImpl extends ServiceImpl<FmbxActivityLikeMapper, FmbxActivityLike> implements IFmbxActivityLikeService {

}
