package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店套餐和房型绑定,相当于票种表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_sku")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.AUTO)
    private Integer skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 活动id
     */
    private Integer xaid;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 供应商id
     */
    private Integer bpId;

    /**
     * sku类型:1-酒店日历房,2-房券
     */
    private Integer skuType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 创建人uid
     */
    private Integer createUid;

    /**
     * 创建人用户名
     */
    private String createUidName;

    /**
     * 最后修改人uid
     */
    private Integer lastUpdateUid;

    /**
     * 最后修改人用户名
     */
    private String lastUpdateUidName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }
    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public String getCreateUidName() {
        return createUidName;
    }

    public void setCreateUidName(String createUidName) {
        this.createUidName = createUidName;
    }
    public Integer getLastUpdateUid() {
        return lastUpdateUid;
    }

    public void setLastUpdateUid(Integer lastUpdateUid) {
        this.lastUpdateUid = lastUpdateUid;
    }
    public String getLastUpdateUidName() {
        return lastUpdateUidName;
    }

    public void setLastUpdateUidName(String lastUpdateUidName) {
        this.lastUpdateUidName = lastUpdateUidName;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxSku{" +
            "skuId=" + skuId +
            ", skuName=" + skuName +
            ", xaid=" + xaid +
            ", bpsId=" + bpsId +
            ", bpId=" + bpId +
            ", skuType=" + skuType +
            ", ctime=" + ctime +
            ", createUid=" + createUid +
            ", createUidName=" + createUidName +
            ", lastUpdateUid=" + lastUpdateUid +
            ", lastUpdateUidName=" + lastUpdateUidName +
            ", utime=" + utime +
        "}";
    }
}
