package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 通用票种包含的可选酒店表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_hotel_reserve_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbHotelReserveInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 预约酒店票种对应的自增ID
     */
    private Integer htId;

    /**
     * 酒店活动ID
     */
    private Integer aid;

    /**
     * 酒店套餐ID
     */
    private Integer packageId;

    /**
     * 酒店票种id
     */
    private Integer ticketId;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 每张次卡最多可预约该酒店的间夜量
     */
    private Integer moreTimes;

    /**
     * 1个间夜量消耗几次
     */
    private Integer expendTimes;

    /**
     * 是否可改约0为否，1为是
     */
    private Integer isChange;

    /**
     * 可改约时间戳
     */
    private Integer changeTime;

    /**
     * 前台每单可改约次数
     */
    private Integer returnTimes;

    /**
     * 是否可电话预约0为否，1为是
     */
    private Integer isPhone;

    /**
     * 可预约电话
     */
    private String phoneNumber;

    /**
     * 是否删除0为否，1为是
     */
    private Integer isDelete;

    /**
     * 状态：0屏蔽,1开启
     */
    private Integer status;

    /**
     * 标准价
     */
    private BigDecimal price;

    /**
     * 标准结算价
     */
    private BigDecimal settlePrice;

    /**
     * 预约说明
     */
    private String note;

    /**
     * 后台操作用户
     */
    private Integer adminUid;

    /**
     * 创建者ID
     */
    private Integer createUid;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getHtId() {
        return htId;
    }

    public void setHtId(Integer htId) {
        this.htId = htId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }
    public Integer getTicketId() {
        return ticketId;
    }

    public void setTicketId(Integer ticketId) {
        this.ticketId = ticketId;
    }
    public String getHotelAddress() {
        return hotelAddress;
    }

    public void setHotelAddress(String hotelAddress) {
        this.hotelAddress = hotelAddress;
    }
    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }
    public Integer getMoreTimes() {
        return moreTimes;
    }

    public void setMoreTimes(Integer moreTimes) {
        this.moreTimes = moreTimes;
    }
    public Integer getExpendTimes() {
        return expendTimes;
    }

    public void setExpendTimes(Integer expendTimes) {
        this.expendTimes = expendTimes;
    }
    public Integer getIsChange() {
        return isChange;
    }

    public void setIsChange(Integer isChange) {
        this.isChange = isChange;
    }
    public Integer getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Integer changeTime) {
        this.changeTime = changeTime;
    }
    public Integer getReturnTimes() {
        return returnTimes;
    }

    public void setReturnTimes(Integer returnTimes) {
        this.returnTimes = returnTimes;
    }
    public Integer getIsPhone() {
        return isPhone;
    }

    public void setIsPhone(Integer isPhone) {
        this.isPhone = isPhone;
    }
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbHotelReserveInfo{" +
            "id=" + id +
            ", htId=" + htId +
            ", aid=" + aid +
            ", packageId=" + packageId +
            ", ticketId=" + ticketId +
            ", hotelAddress=" + hotelAddress +
            ", hotelName=" + hotelName +
            ", moreTimes=" + moreTimes +
            ", expendTimes=" + expendTimes +
            ", isChange=" + isChange +
            ", changeTime=" + changeTime +
            ", returnTimes=" + returnTimes +
            ", isPhone=" + isPhone +
            ", phoneNumber=" + phoneNumber +
            ", isDelete=" + isDelete +
            ", status=" + status +
            ", price=" + price +
            ", settlePrice=" + settlePrice +
            ", note=" + note +
            ", adminUid=" + adminUid +
            ", createUid=" + createUid +
            ", utime=" + utime +
            ", ctime=" + ctime +
        "}";
    }
}
