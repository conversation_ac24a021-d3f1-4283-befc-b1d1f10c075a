package main.java.com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 活动标签表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_activity_tag")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbActivityTag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 活动的ID
     */
    private Integer aid;

    /**
     * 标签名字
     */
    private String tagName;

    /**
     * 始终显示：0是，1定时;默认0；
     */
    private Integer alwaysShow;

    /**
     * 定时显示-开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 定时显示-结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 对应的标签管理列表id
     */
    private Integer tagListId;

    /**
     * 对应的标签分类id
     */
    private Integer tagCatId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }
    public Integer getAlwaysShow() {
        return alwaysShow;
    }

    public void setAlwaysShow(Integer alwaysShow) {
        this.alwaysShow = alwaysShow;
    }
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public Integer getTagListId() {
        return tagListId;
    }

    public void setTagListId(Integer tagListId) {
        this.tagListId = tagListId;
    }
    public Integer getTagCatId() {
        return tagCatId;
    }

    public void setTagCatId(Integer tagCatId) {
        this.tagCatId = tagCatId;
    }

    @Override
    public String toString() {
        return "FmbActivityTag{" +
            "id=" + id +
            ", aid=" + aid +
            ", tagName=" + tagName +
            ", alwaysShow=" + alwaysShow +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", tagListId=" + tagListId +
            ", tagCatId=" + tagCatId +
        "}";
    }
}
