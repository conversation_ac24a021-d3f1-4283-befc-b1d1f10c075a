package main.java.com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 部门列表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_department_list")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbDepartmentList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @TableId(value = "department_id", type = IdType.AUTO)
    private Integer departmentId;

    /**
     * 父ID
     */
    private Integer parentid;

    /**
     * 所有父ID
     */
    private String arrparentid;

    /**
     * 子ID数目
     */
    private Boolean child;

    /**
     * 所有子ID
     */
    private String arrchildid;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门负责人uid
     */
    private Integer uid;

    /**
     * 列表顺序,越大越靠前
     */
    private Integer listorder;

    private Boolean isShield;

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }
    public Integer getParentid() {
        return parentid;
    }

    public void setParentid(Integer parentid) {
        this.parentid = parentid;
    }
    public String getArrparentid() {
        return arrparentid;
    }

    public void setArrparentid(String arrparentid) {
        this.arrparentid = arrparentid;
    }
    public Boolean getChild() {
        return child;
    }

    public void setChild(Boolean child) {
        this.child = child;
    }
    public String getArrchildid() {
        return arrchildid;
    }

    public void setArrchildid(String arrchildid) {
        this.arrchildid = arrchildid;
    }
    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getListorder() {
        return listorder;
    }

    public void setListorder(Integer listorder) {
        this.listorder = listorder;
    }
    public Boolean getIsShield() {
        return isShield;
    }

    public void setIsShield(Boolean isShield) {
        this.isShield = isShield;
    }

    @Override
    public String toString() {
        return "FmbDepartmentList{" +
            "departmentId=" + departmentId +
            ", parentid=" + parentid +
            ", arrparentid=" + arrparentid +
            ", child=" + child +
            ", arrchildid=" + arrchildid +
            ", departmentName=" + departmentName +
            ", uid=" + uid +
            ", listorder=" + listorder +
            ", isShield=" + isShield +
        "}";
    }
}
