package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 商户图片视频
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bps_media")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpsMedia implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 商户房型id
     */
    private Integer roomId;

    /**
     * 附件ID
     */
    private Integer mediaId;

    /**
     * 类型:1-图片,2-视频
     */
    private Integer type;

    /**
     * 名称描述
     */
    private String description;

    /**
     * 图片分组:1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
     */
    private Integer groupval;

    /**
     * 是否有效:0-无效,1-有效
     */
    private Integer status;

    /**
     * 台后操作用户
     */
    private Integer adminUid;

    /**
     * 后台用户类型:1-父母邦后台,2-供应商后台
     */
    private Integer adminType;

    /**
     * 后台用户名字
     */
    private String adminName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 最后操作人
     */
    private String lastopAdminInfo;

    /**
     * 排序值小的排前面
     */
    private Integer rankval;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public Integer getMediaId() {
        return mediaId;
    }

    public void setMediaId(Integer mediaId) {
        this.mediaId = mediaId;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public Integer getGroupval() {
        return groupval;
    }

    public void setGroupval(Integer groupval) {
        this.groupval = groupval;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public String getLastopAdminInfo() {
        return lastopAdminInfo;
    }

    public void setLastopAdminInfo(String lastopAdminInfo) {
        this.lastopAdminInfo = lastopAdminInfo;
    }
    public Integer getRankval() {
        return rankval;
    }

    public void setRankval(Integer rankval) {
        this.rankval = rankval;
    }

    @Override
    public String toString() {
        return "FmbxBpsMedia{" +
            "id=" + id +
            ", bpsId=" + bpsId +
            ", roomId=" + roomId +
            ", mediaId=" + mediaId +
            ", type=" + type +
            ", description=" + description +
            ", groupval=" + groupval +
            ", status=" + status +
            ", adminUid=" + adminUid +
            ", adminType=" + adminType +
            ", adminName=" + adminName +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", lastopAdminInfo=" + lastopAdminInfo +
            ", rankval=" + rankval +
        "}";
    }
}
