package main.java.com.fmb.server2022.fmb_new.service.impl;

import com.fmb.server2022.fmb_new.entity.FmbDepartmentList;
import com.fmb.server2022.fmb_new.mapper.FmbDepartmentListMapper;
import com.fmb.server2022.fmb_new.service.IFmbDepartmentListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 部门列表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-26
 */
@Service
public class FmbDepartmentListServiceImpl extends ServiceImpl<FmbDepartmentListMapper, FmbDepartmentList> implements IFmbDepartmentListService {

}
