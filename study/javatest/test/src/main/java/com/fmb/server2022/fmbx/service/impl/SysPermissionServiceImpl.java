package main.java.com.fmb.server2022.fmbx.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fmb.server2022.fmbx.entity.SysPermission;
import com.fmb.server2022.fmbx.mapper.SysPermissionMapper;
import com.fmb.server2022.fmbx.service.ISysPermissionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 后台权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements ISysPermissionService {


    public List<SysPermission> listByRoleid(Integer roleid) {

        return baseMapper.selectByRoleid(roleid);
    }
}
