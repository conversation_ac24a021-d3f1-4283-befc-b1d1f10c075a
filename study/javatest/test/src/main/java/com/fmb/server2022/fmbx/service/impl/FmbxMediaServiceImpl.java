package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxMedia;
import com.fmb.server2022.fmbx.mapper.FmbxMediaMapper;
import com.fmb.server2022.fmbx.service.IFmbxMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 用户上传的各种图片和视频 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-25
 */
@Service
public class FmbxMediaServiceImpl extends ServiceImpl<FmbxMediaMapper, FmbxMedia> implements IFmbxMediaService {

}
