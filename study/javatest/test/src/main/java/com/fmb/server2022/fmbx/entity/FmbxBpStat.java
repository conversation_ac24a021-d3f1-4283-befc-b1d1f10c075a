package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商统计信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_stat")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpStat implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Integer bpId;

    /**
     * 销售统计,订单数
     */
    private Integer saleOrderCount;

    /**
     * 销售统计,货物数
     */
    private Integer saleGoodsCount;

    /**
     * 销售统计,销售金额
     */
    private BigDecimal saleOrderMoney;

    /**
     * 登录信息,登录次数
     */
    private Integer loginStatCount;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime loginStatLasttime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getSaleOrderCount() {
        return saleOrderCount;
    }

    public void setSaleOrderCount(Integer saleOrderCount) {
        this.saleOrderCount = saleOrderCount;
    }
    public Integer getSaleGoodsCount() {
        return saleGoodsCount;
    }

    public void setSaleGoodsCount(Integer saleGoodsCount) {
        this.saleGoodsCount = saleGoodsCount;
    }
    public BigDecimal getSaleOrderMoney() {
        return saleOrderMoney;
    }

    public void setSaleOrderMoney(BigDecimal saleOrderMoney) {
        this.saleOrderMoney = saleOrderMoney;
    }
    public Integer getLoginStatCount() {
        return loginStatCount;
    }

    public void setLoginStatCount(Integer loginStatCount) {
        this.loginStatCount = loginStatCount;
    }
    public LocalDateTime getLoginStatLasttime() {
        return loginStatLasttime;
    }

    public void setLoginStatLasttime(LocalDateTime loginStatLasttime) {
        this.loginStatLasttime = loginStatLasttime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxBpStat{" +
            "bpId=" + bpId +
            ", saleOrderCount=" + saleOrderCount +
            ", saleGoodsCount=" + saleGoodsCount +
            ", saleOrderMoney=" + saleOrderMoney +
            ", loginStatCount=" + loginStatCount +
            ", loginStatLasttime=" + loginStatLasttime +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
