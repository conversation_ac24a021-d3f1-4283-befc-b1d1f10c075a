package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店套餐房型日期价格表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_roomdate_price")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuiteRoomdatePrice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "price_id", type = IdType.AUTO)
    private Integer priceId;

    /**
     * skuid
     */
    private Integer suiteRoomSkuId;

    /**
     * 套餐id(冗余)
     */
    private Integer suiteId;

    /**
     * 商户房型id(冗余)
     */
    private Integer roomId;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate targetDate;

    /**
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getPriceId() {
        return priceId;
    }

    public void setPriceId(Integer priceId) {
        this.priceId = priceId;
    }
    public Integer getSuiteRoomSkuId() {
        return suiteRoomSkuId;
    }

    public void setSuiteRoomSkuId(Integer suiteRoomSkuId) {
        this.suiteRoomSkuId = suiteRoomSkuId;
    }
    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public LocalDate getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(LocalDate targetDate) {
        this.targetDate = targetDate;
    }
    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxSuiteRoomdatePrice{" +
            "priceId=" + priceId +
            ", suiteRoomSkuId=" + suiteRoomSkuId +
            ", suiteId=" + suiteId +
            ", roomId=" + roomId +
            ", targetDate=" + targetDate +
            ", marketPrice=" + marketPrice +
            ", goodsPrice=" + goodsPrice +
            ", settlePrice=" + settlePrice +
            ", versionNum=" + versionNum +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
