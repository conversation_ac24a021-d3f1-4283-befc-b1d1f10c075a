package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpMedia;
import com.fmb.server2022.fmbx.mapper.FmbxBpMediaMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商文件资源表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-25
 */
@Service
public class FmbxBpMediaServiceImpl extends ServiceImpl<FmbxBpMediaMapper, FmbxBpMedia> implements IFmbxBpMediaService {

}
