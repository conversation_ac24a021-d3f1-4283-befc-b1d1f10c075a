package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbHotelReserveInfo;
import com.fmb.server2022.fmbx.mapper.FmbHotelReserveInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbHotelReserveInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 通用票种包含的可选酒店表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service
public class FmbHotelReserveInfoServiceImpl extends ServiceImpl<FmbHotelReserveInfoMapper, FmbHotelReserveInfo> implements IFmbHotelReserveInfoService {

}
