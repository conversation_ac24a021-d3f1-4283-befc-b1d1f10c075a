package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 分销商与C端用户关系表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_mycps_bind_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbMycpsBindUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer uid;

    /**
     * 分销商id
     */
    private Integer mid;

    /**
     * 分销商名称
     */
    private String mname;

    /**
     * 分销商mid对应的uid
     */
    private Integer mycpsUid;

    /**
     * 微信公用unionid
     */
    private String weixinUnionid;

    /**
     * 专属粉丝：1是；0否
     */
    private String fansStatus;

    /**
     * 专属买家：1是；0否
     */
    private String buyerStatus;

    /**
     * 专属粉丝(关注)创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime fansCtime;

    /**
     * 专属粉丝(关注)取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime fansCancelTime;

    /**
     * 专属买家绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime buyerCtime;

    /**
     * 专属买家解绑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime buyerCancelTime;

    /**
     * 是否有微信图像：1有；0没有
     */
    private String weixinHeadimg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }
    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
    public Integer getMycpsUid() {
        return mycpsUid;
    }

    public void setMycpsUid(Integer mycpsUid) {
        this.mycpsUid = mycpsUid;
    }
    public String getWeixinUnionid() {
        return weixinUnionid;
    }

    public void setWeixinUnionid(String weixinUnionid) {
        this.weixinUnionid = weixinUnionid;
    }
    public String getFansStatus() {
        return fansStatus;
    }

    public void setFansStatus(String fansStatus) {
        this.fansStatus = fansStatus;
    }
    public String getBuyerStatus() {
        return buyerStatus;
    }

    public void setBuyerStatus(String buyerStatus) {
        this.buyerStatus = buyerStatus;
    }
    public LocalDateTime getFansCtime() {
        return fansCtime;
    }

    public void setFansCtime(LocalDateTime fansCtime) {
        this.fansCtime = fansCtime;
    }
    public LocalDateTime getFansCancelTime() {
        return fansCancelTime;
    }

    public void setFansCancelTime(LocalDateTime fansCancelTime) {
        this.fansCancelTime = fansCancelTime;
    }
    public LocalDateTime getBuyerCtime() {
        return buyerCtime;
    }

    public void setBuyerCtime(LocalDateTime buyerCtime) {
        this.buyerCtime = buyerCtime;
    }
    public LocalDateTime getBuyerCancelTime() {
        return buyerCancelTime;
    }

    public void setBuyerCancelTime(LocalDateTime buyerCancelTime) {
        this.buyerCancelTime = buyerCancelTime;
    }
    public String getWeixinHeadimg() {
        return weixinHeadimg;
    }

    public void setWeixinHeadimg(String weixinHeadimg) {
        this.weixinHeadimg = weixinHeadimg;
    }

    @Override
    public String toString() {
        return "FmbMycpsBindUser{" +
            "id=" + id +
            ", uid=" + uid +
            ", mid=" + mid +
            ", mname=" + mname +
            ", mycpsUid=" + mycpsUid +
            ", weixinUnionid=" + weixinUnionid +
            ", fansStatus=" + fansStatus +
            ", buyerStatus=" + buyerStatus +
            ", fansCtime=" + fansCtime +
            ", fansCancelTime=" + fansCancelTime +
            ", buyerCtime=" + buyerCtime +
            ", buyerCancelTime=" + buyerCancelTime +
            ", weixinHeadimg=" + weixinHeadimg +
        "}";
    }
}
