package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 套餐表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuite implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final Map<Integer, String> isInvoiceMap;
    public static final Map<Integer, String> returnPolicyMap;
    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "不支持开票");
            map.put(1, "父母邦开票");
            map.put(2, "商家开票");
            isInvoiceMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "不支持");
            map.put(1, "有效期结束前未使用可退");
            map.put(2, "过期后未使用可退");
            map.put(3, "未使用随时退 (有效期内+过期后)");
            returnPolicyMap = Collections.unmodifiableMap(map);
        }
    }


    @TableId(value = "suite_id", type = IdType.AUTO)
    private Integer suiteId;

    /**
     * fmbx活动主表id
     */
    private Integer xaid;

    /**
     * 套餐名字
     */
    private String name;

    /**
     * 套餐食享(json)
     */
    private String suiteContent;

    /**
     * 总间夜量
     */
    private Integer totalNeight;

    /**
     * 连住晚数
     */
    private Integer nightMin;

    /**
     * 价格输入方式:1-加价模式,2-整价模式
     */
    private Integer priceInputType;

    /**
     * 是否有日历房订单:0-否,1-是
     */
    private Integer flagHaveOrder;

    /**
     * 是否有房券订单:0-否,1-是
     */
    private Integer flagHaveOrderReserve;

    /**
     * 日历房:0-否,1-是
     */
    private Integer flagStandardHotel;

    /**
     * 预售房券:0-否,1-是
     */
    private Integer flagHotelReserve;

    /**
     * 房券按房型拆分类型:1-拆分房券,2-单一房券
     */
    private Integer hotelReserveSplit;

    /**
     * 购买限制:0-无限制,1-有限制
     */
    private Integer buyLimit;

    /**
     * 预售房券需提前预定天数
     */
    private Integer preReserveDay;

    /**
     * 预售房券需提前预定时间(String)
     */
    private String preReserveTime;

    /**
     * 提前预定秒数(计算值)
     */
    private Integer preReserveSecond;

    /**
     * 下单后未付款订单关闭时间(单位秒)
     */
    private Integer autoCloseOrderSecond;

    /**
     * 证件资料:0-不需要,1-需提供一位用户身份信息,2-需提供所有用户身份信息
     */
    private Integer certFlag;

    /**
     * 证件信息提示文本
     */
    private String certTip;

    /**
     * 风险提示:0-不需要,1-需要
     */
    private Integer flagRiskWarning;

    /**
     * 风险提示文本
     */
    private String riskWarningTip;

    /**
     * 风险提示短信开关:0-不发短信,1-发短信
     */
    private Integer riskSmsFlag;

    /**
     * 风险提示短信内容
     */
    private String riskSmsContent;

    /**
     * 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
     */
    private Integer isReturn;

    /**
     * 退换政策
     */
    private String returnPolicy;

    /**
     * 退款有效期天数
     */
    private Integer returnValidDay;

    /**
     * 退款有效期时间
     */
    private String returnValidTime;

    /**
     * 退款有效期秒数(计算值)
     */
    private Integer returnValidSecond;

    /**
     * 开启自助退款:0-否,1-是
     */
    private Integer isAutoReturn;

    /**
     * 发票政策:0-不支持开票,1-父母邦开票,2-商家开票
     */
    private Integer isInvoice;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 排序值,值越大越靠前
     */
    private Integer sortValue;

    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getSuiteContent() {
        return suiteContent;
    }

    public void setSuiteContent(String suiteContent) {
        this.suiteContent = suiteContent;
    }
    public Integer getTotalNeight() {
        return totalNeight;
    }

    public void setTotalNeight(Integer totalNeight) {
        this.totalNeight = totalNeight;
    }
    public Integer getNightMin() {
        return nightMin;
    }

    public void setNightMin(Integer nightMin) {
        this.nightMin = nightMin;
    }
    public Integer getPriceInputType() {
        return priceInputType;
    }

    public void setPriceInputType(Integer priceInputType) {
        this.priceInputType = priceInputType;
    }
    public Integer getFlagHaveOrder() {
        return flagHaveOrder;
    }

    public void setFlagHaveOrder(Integer flagHaveOrder) {
        this.flagHaveOrder = flagHaveOrder;
    }
    public Integer getFlagHaveOrderReserve() {
        return flagHaveOrderReserve;
    }

    public void setFlagHaveOrderReserve(Integer flagHaveOrderReserve) {
        this.flagHaveOrderReserve = flagHaveOrderReserve;
    }
    public Integer getFlagStandardHotel() {
        return flagStandardHotel;
    }

    public void setFlagStandardHotel(Integer flagStandardHotel) {
        this.flagStandardHotel = flagStandardHotel;
    }
    public Integer getFlagHotelReserve() {
        return flagHotelReserve;
    }

    public void setFlagHotelReserve(Integer flagHotelReserve) {
        this.flagHotelReserve = flagHotelReserve;
    }
    public Integer getHotelReserveSplit() {
        return hotelReserveSplit;
    }

    public void setHotelReserveSplit(Integer hotelReserveSplit) {
        this.hotelReserveSplit = hotelReserveSplit;
    }
    public Integer getBuyLimit() {
        return buyLimit;
    }

    public void setBuyLimit(Integer buyLimit) {
        this.buyLimit = buyLimit;
    }
    public Integer getPreReserveDay() {
        return preReserveDay;
    }

    public void setPreReserveDay(Integer preReserveDay) {
        this.preReserveDay = preReserveDay;
    }
    public String getPreReserveTime() {
        return preReserveTime;
    }

    public void setPreReserveTime(String preReserveTime) {
        this.preReserveTime = preReserveTime;
    }
    public Integer getPreReserveSecond() {
        return preReserveSecond;
    }

    public void setPreReserveSecond(Integer preReserveSecond) {
        this.preReserveSecond = preReserveSecond;
    }
    public Integer getAutoCloseOrderSecond() {
        return autoCloseOrderSecond;
    }

    public void setAutoCloseOrderSecond(Integer autoCloseOrderSecond) {
        this.autoCloseOrderSecond = autoCloseOrderSecond;
    }
    public Integer getCertFlag() {
        return certFlag;
    }

    public void setCertFlag(Integer certFlag) {
        this.certFlag = certFlag;
    }
    public String getCertTip() {
        return certTip;
    }

    public void setCertTip(String certTip) {
        this.certTip = certTip;
    }
    public Integer getFlagRiskWarning() {
        return flagRiskWarning;
    }

    public void setFlagRiskWarning(Integer flagRiskWarning) {
        this.flagRiskWarning = flagRiskWarning;
    }
    public String getRiskWarningTip() {
        return riskWarningTip;
    }

    public void setRiskWarningTip(String riskWarningTip) {
        this.riskWarningTip = riskWarningTip;
    }
    public Integer getRiskSmsFlag() {
        return riskSmsFlag;
    }

    public void setRiskSmsFlag(Integer riskSmsFlag) {
        this.riskSmsFlag = riskSmsFlag;
    }
    public String getRiskSmsContent() {
        return riskSmsContent;
    }

    public void setRiskSmsContent(String riskSmsContent) {
        this.riskSmsContent = riskSmsContent;
    }
    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }
    public String getReturnPolicy() {
        return returnPolicy;
    }

    public void setReturnPolicy(String returnPolicy) {
        this.returnPolicy = returnPolicy;
    }
    public Integer getReturnValidDay() {
        return returnValidDay;
    }

    public void setReturnValidDay(Integer returnValidDay) {
        this.returnValidDay = returnValidDay;
    }
    public String getReturnValidTime() {
        return returnValidTime;
    }

    public void setReturnValidTime(String returnValidTime) {
        this.returnValidTime = returnValidTime;
    }
    public Integer getReturnValidSecond() {
        return returnValidSecond;
    }

    public void setReturnValidSecond(Integer returnValidSecond) {
        this.returnValidSecond = returnValidSecond;
    }
    public Integer getIsAutoReturn() {
        return isAutoReturn;
    }

    public void setIsAutoReturn(Integer isAutoReturn) {
        this.isAutoReturn = isAutoReturn;
    }
    public Integer getIsInvoice() {
        return isInvoice;
    }

    public void setIsInvoice(Integer isInvoice) {
        this.isInvoice = isInvoice;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }

    public String getInvoiceTypeStr() {
        return isInvoiceMap.get(isInvoice);
    }

    public String getReturnPolicyStr() {
        return returnPolicyMap.get(isReturn)+ returnPolicy;
    }



    @Override
    public String toString() {
        return "FmbxSuite{" +
            "suiteId=" + suiteId +
            ", xaid=" + xaid +
            ", name=" + name +
            ", suiteContent=" + suiteContent +
            ", totalNeight=" + totalNeight +
            ", nightMin=" + nightMin +
            ", priceInputType=" + priceInputType +
            ", flagHaveOrder=" + flagHaveOrder +
            ", flagHaveOrderReserve=" + flagHaveOrderReserve +
            ", flagStandardHotel=" + flagStandardHotel +
            ", flagHotelReserve=" + flagHotelReserve +
            ", hotelReserveSplit=" + hotelReserveSplit +
            ", buyLimit=" + buyLimit +
            ", preReserveDay=" + preReserveDay +
            ", preReserveTime=" + preReserveTime +
            ", preReserveSecond=" + preReserveSecond +
            ", autoCloseOrderSecond=" + autoCloseOrderSecond +
            ", certFlag=" + certFlag +
            ", certTip=" + certTip +
            ", flagRiskWarning=" + flagRiskWarning +
            ", riskWarningTip=" + riskWarningTip +
            ", riskSmsFlag=" + riskSmsFlag +
            ", riskSmsContent=" + riskSmsContent +
            ", isReturn=" + isReturn +
            ", returnPolicy=" + returnPolicy +
            ", returnValidDay=" + returnValidDay +
            ", returnValidTime=" + returnValidTime +
            ", returnValidSecond=" + returnValidSecond +
            ", isAutoReturn=" + isAutoReturn +
            ", isInvoice=" + isInvoice +
            ", versionNum=" + versionNum +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", sortValue=" + sortValue +
        "}";
    }
}
