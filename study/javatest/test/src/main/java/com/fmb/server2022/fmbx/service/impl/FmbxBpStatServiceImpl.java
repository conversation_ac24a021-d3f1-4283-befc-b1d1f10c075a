package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpStat;
import com.fmb.server2022.fmbx.mapper.FmbxBpStatMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpStatService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商统计信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class FmbxBpStatServiceImpl extends ServiceImpl<FmbxBpStatMapper, FmbxBpStat> implements IFmbxBpStatService {

}
