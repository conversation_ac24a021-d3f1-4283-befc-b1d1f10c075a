package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 退货操作记录表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_returns_action")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderReturnsAction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "action_id", type = IdType.AUTO)
    private Integer actionId;

    /**
     * 后台用户ID
     */
    private Integer adminUid;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单表中对应商品记录ID
     */
    private Integer recId;

    /**
     * 具体票种商品ID
     */
    private Integer goodsId;

    /**
     * 退货ID
     */
    private Integer returnId;

    /**
     * 退货状态   0待审核，1审核通过，2完成退货，3拒绝退货,4确认收到退货
     */
    private Integer returnStatus;

    /**
     * 操作备注
     */
    private String actionNote;

    /**
     * 生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }
    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }
    public String getActionNote() {
        return actionNote;
    }

    public void setActionNote(String actionNote) {
        this.actionNote = actionNote;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "FmbOrderReturnsAction{" +
            "actionId=" + actionId +
            ", adminUid=" + adminUid +
            ", orderSn=" + orderSn +
            ", recId=" + recId +
            ", goodsId=" + goodsId +
            ", returnId=" + returnId +
            ", returnStatus=" + returnStatus +
            ", actionNote=" + actionNote +
            ", createTime=" + createTime +
        "}";
    }
}
