package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbCouponForbidden;
import com.fmb.server2022.fmbx.mapper.FmbCouponForbiddenMapper;
import com.fmb.server2022.fmbx.service.IFmbCouponForbiddenService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 券组的禁用产品表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbCouponForbiddenServiceImpl extends ServiceImpl<FmbCouponForbiddenMapper, FmbCouponForbidden> implements IFmbCouponForbiddenService {

}
