package main.java.com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 活动主体表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@TableName("fmb_new_activity")
public class FmbNewActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @TableId(value = "aid", type = IdType.AUTO)

    private Integer aid;

    /**
     * 活动标题
     */

    private String title;

    /**
     * 活动英文标题
     */

    private String englishTitle;

    /**
     * 活动副标题
     */

    private String subTitle;

    /**
     * 匹配商户ID
     */

    private Integer shopId;

    /**
     * 商家类型
     */

    private String shopType;

    /**
     * 1:普通活动;2:app专享活动;3:app专享价格活动
     */

    private Boolean activityType;

    /**
     * 1,'演出展览',2,'景点门票',3,'通用商品',4,'酒店住宿',5,'报名活动',6,'展示活动'
     */

    private Integer activityGroupType;

    /**
     * 活动banner图
     */

    private String banner;

    /**
     * 座位图
     */

    private String seatImg;

    /**
     * 活动banner多图
     */

    private String bannerList;

    /**
     * 最小年龄
     */

    private Integer ageMin;

    /**
     * 最大年龄
     */

    private Integer ageMax;

    /**
     * 适合年龄描述，替代最小年龄和最大年龄
     */

    private String ageDesc;

    /**
     * 等级ID
     */

    private Integer gradeLevel;

    /**
     * 消耗积分
     */

    private Integer useScore;

    /**
     * 活动分类
     */

    private Integer categoryId;

    /**
     * 票务分类
     */

    private Integer catId;

    /**
     * 收入类型
     */

    private Integer incomeId;

    /**
     * 城市ID(fmb_city)
     */

    private Integer cityId;

    /**
     * 可售卖分站
     */

    private String salesCityIds;

    /**
     * 活动(产品)所属业务线：1,(弃用)老长线;2,酒店;3,北京活动票务;5,上海活动票务;7,(废弃)其他;11-北京长线;12-上海长线
     */

    private Boolean businessType;

    /**
     * 省市ID
     */

    private Integer provincesId;

    /**
     * 区域id(fmb_area)
     */

    private Integer areaId;

    /**
     * 非直辖市的区ID
     */

    private Integer districtId;

    /**
     * 商圈id(fmb_business)
     */

    private Integer businessId;

    /**
     * 目的地省市IDS
     */

    private String destinationProvincesIds;

    /**
     * 目的地城市IDS
     */

    private String destinationCityIds;

    /**
     * 位置经度
     */

    private Float longitude;

    /**
     * 位置经度
     */

    private Float latitude;

    /**
     * 活动详细地址
     */

    private String address;

    /**
     * 参与人数
     */

    private Integer peopleNum;

    /**
     * 活动费用
     */

    private String price;

    /**
     * 卖出活动票的张数
     */

    private Integer sellNum;

    /**
     * 活动管理者UID
     */

    private Integer adminUid;

    /**
     * 活动创建者UID
     */

    private Integer creatorUid;

    /**
     * 二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
     */
    @TableField(exist = false)
    private Integer isConfirm;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 活动报名开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyStartTime;

    /**
     * 活动报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyEndTime;

    /**
     * 活动负责人
     */

    private String manager;

    /**
     * 活动负责人手机
     */

    private String managerMobile;

    /**
     * 0默认，不推荐，1为推荐
     */

    private String recommend;

    /**
     * 是否可报名:0:不可报名
     */

    private Boolean isApply;

    /**
     * 是否可购买:0:不可购买
     */

    private Boolean isSell;

    /**
     * 报名所需积分
     */

    private Integer applyScore;

    /**
     * 0:未知或非卖品,1:关闭,2:无库存,4:已过期,8:已屏蔽,16:在售中
     */

    private Integer sellStat;

    /**
     * 是否可积分兑换:0:不可兑换;1:可兑换
     */

    private Boolean isCreditExchange;

    /**
     * 兑换花费的积分值
     */

    private Integer exchangeScore;

    /**
     * 兑换名额
     */

    private Integer exchangeNum;

    /**
     * 最低售价
     */

    private Float sellMinPrice;

    /**
     * 市场最低价格
     */

    private Float marketMinPrice;

    /**
     * 活动列表排序
     */

    private Integer listorder;

    /**
     * 点击数量
     */

    private Integer clickNum;

    /**
     * 等待，通过，屏蔽
     */

    private String status;

    /**
     * 该活动是否有有效的结伴游套餐0-无1-有
     */

    private Integer partnerStatus;

    /**
     * 结伴是否可开团0-否1-是
     */

    private Integer partnerCreateStatus;

    /**
     * 小程序是否展示此活动 -- 1：展示 2：不展示
     */

    private Integer wxappShow;

    /**
     * 购票链接
     */

    private String ticket;

    /**
     * 相关活动推荐通过分号分割
     */

    private String relateAid;

    /**
     * 报名活动直接购买配置
     */

    private Integer buyAid;

    /**
     * 显示平台：0,全平台;1,h5;2,web;3,app;9,全部不展示;10,小程序
     */

    private String platId;

    /**
     * 我司编辑对接人
     */

    private Integer editorUid;

    /**
     * 我司市场对接人
     */

    private Integer marketUid;

    /**
     * 联动更新,这个时间更新取决于 fmb_new_activity;fmb_group_tickets;fmb_tickets  三个表中的任何更新
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime linkUtime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime publishTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getEnglishTitle() {
        return englishTitle;
    }

    public void setEnglishTitle(String englishTitle) {
        this.englishTitle = englishTitle;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public Boolean getActivityType() {
        return activityType;
    }

    public void setActivityType(Boolean activityType) {
        this.activityType = activityType;
    }

    public Integer getActivityGroupType() {
        return activityGroupType;
    }

    public void setActivityGroupType(Integer activityGroupType) {
        this.activityGroupType = activityGroupType;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public String getSeatImg() {
        return seatImg;
    }

    public void setSeatImg(String seatImg) {
        this.seatImg = seatImg;
    }

    public String getBannerList() {
        return bannerList;
    }

    public void setBannerList(String bannerList) {
        this.bannerList = bannerList;
    }

    public Integer getAgeMin() {
        return ageMin;
    }

    public void setAgeMin(Integer ageMin) {
        this.ageMin = ageMin;
    }

    public Integer getAgeMax() {
        return ageMax;
    }

    public void setAgeMax(Integer ageMax) {
        this.ageMax = ageMax;
    }

    public String getAgeDesc() {
        return ageDesc;
    }

    public void setAgeDesc(String ageDesc) {
        this.ageDesc = ageDesc;
    }

    public Integer getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(Integer gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    public Integer getUseScore() {
        return useScore;
    }

    public void setUseScore(Integer useScore) {
        this.useScore = useScore;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCatId() {
        return catId;
    }

    public void setCatId(Integer catId) {
        this.catId = catId;
    }

    public Integer getIncomeId() {
        return incomeId;
    }

    public void setIncomeId(Integer incomeId) {
        this.incomeId = incomeId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getSalesCityIds() {
        return salesCityIds;
    }

    public void setSalesCityIds(String salesCityIds) {
        this.salesCityIds = salesCityIds;
    }

    public Boolean getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Boolean businessType) {
        this.businessType = businessType;
    }

    public Integer getProvincesId() {
        return provincesId;
    }

    public void setProvincesId(Integer provincesId) {
        this.provincesId = provincesId;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getDestinationProvincesIds() {
        return destinationProvincesIds;
    }

    public void setDestinationProvincesIds(String destinationProvincesIds) {
        this.destinationProvincesIds = destinationProvincesIds;
    }

    public String getDestinationCityIds() {
        return destinationCityIds;
    }

    public void setDestinationCityIds(String destinationCityIds) {
        this.destinationCityIds = destinationCityIds;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPeopleNum() {
        return peopleNum;
    }

    public void setPeopleNum(Integer peopleNum) {
        this.peopleNum = peopleNum;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getSellNum() {
        return sellNum;
    }

    public void setSellNum(Integer sellNum) {
        this.sellNum = sellNum;
    }

    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }

    public Integer getCreatorUid() {
        return creatorUid;
    }

    public void setCreatorUid(Integer creatorUid) {
        this.creatorUid = creatorUid;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getApplyStartTime() {
        return applyStartTime;
    }

    public void setApplyStartTime(LocalDateTime applyStartTime) {
        this.applyStartTime = applyStartTime;
    }

    public LocalDateTime getApplyEndTime() {
        return applyEndTime;
    }

    public void setApplyEndTime(LocalDateTime applyEndTime) {
        this.applyEndTime = applyEndTime;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getManagerMobile() {
        return managerMobile;
    }

    public void setManagerMobile(String managerMobile) {
        this.managerMobile = managerMobile;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public Boolean getIsApply() {
        return isApply;
    }

    public void setIsApply(Boolean isApply) {
        this.isApply = isApply;
    }

    public Boolean getIsSell() {
        return isSell;
    }

    public void setIsSell(Boolean isSell) {
        this.isSell = isSell;
    }

    public Integer getApplyScore() {
        return applyScore;
    }

    public void setApplyScore(Integer applyScore) {
        this.applyScore = applyScore;
    }

    public Integer getSellStat() {
        return sellStat;
    }

    public void setSellStat(Integer sellStat) {
        this.sellStat = sellStat;
    }

    public Boolean getIsCreditExchange() {
        return isCreditExchange;
    }

    public void setIsCreditExchange(Boolean isCreditExchange) {
        this.isCreditExchange = isCreditExchange;
    }

    public Integer getExchangeScore() {
        return exchangeScore;
    }

    public void setExchangeScore(Integer exchangeScore) {
        this.exchangeScore = exchangeScore;
    }

    public Integer getExchangeNum() {
        return exchangeNum;
    }

    public void setExchangeNum(Integer exchangeNum) {
        this.exchangeNum = exchangeNum;
    }

    public Float getSellMinPrice() {
        return sellMinPrice;
    }

    public void setSellMinPrice(Float sellMinPrice) {
        this.sellMinPrice = sellMinPrice;
    }

    public Float getMarketMinPrice() {
        return marketMinPrice;
    }

    public void setMarketMinPrice(Float marketMinPrice) {
        this.marketMinPrice = marketMinPrice;
    }

    public Integer getListorder() {
        return listorder;
    }

    public void setListorder(Integer listorder) {
        this.listorder = listorder;
    }

    public Integer getClickNum() {
        return clickNum;
    }

    public void setClickNum(Integer clickNum) {
        this.clickNum = clickNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Integer partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public Integer getPartnerCreateStatus() {
        return partnerCreateStatus;
    }

    public void setPartnerCreateStatus(Integer partnerCreateStatus) {
        this.partnerCreateStatus = partnerCreateStatus;
    }

    public Integer getWxappShow() {
        return wxappShow;
    }

    public void setWxappShow(Integer wxappShow) {
        this.wxappShow = wxappShow;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getRelateAid() {
        return relateAid;
    }

    public void setRelateAid(String relateAid) {
        this.relateAid = relateAid;
    }

    public Integer getBuyAid() {
        return buyAid;
    }

    public void setBuyAid(Integer buyAid) {
        this.buyAid = buyAid;
    }

    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }

    public Integer getEditorUid() {
        return editorUid;
    }

    public void setEditorUid(Integer editorUid) {
        this.editorUid = editorUid;
    }

    public Integer getMarketUid() {
        return marketUid;
    }

    public void setMarketUid(Integer marketUid) {
        this.marketUid = marketUid;
    }

    public LocalDateTime getLinkUtime() {
        return linkUtime;
    }

    public void setLinkUtime(LocalDateTime linkUtime) {
        this.linkUtime = linkUtime;
    }

    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    public LocalDateTime getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }

    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }

    @Override
    public String toString() {
        return "FmbNewActivity{" +
                "aid=" + aid +
                ", title='" + title + '\'' +
                ", englishTitle='" + englishTitle + '\'' +
                ", subTitle='" + subTitle + '\'' +
                ", shopId=" + shopId +
                ", shopType='" + shopType + '\'' +
                ", activityType=" + activityType +
                ", activityGroupType=" + activityGroupType +
                ", banner='" + banner + '\'' +
                ", seatImg='" + seatImg + '\'' +
                ", bannerList='" + bannerList + '\'' +
                ", ageMin=" + ageMin +
                ", ageMax=" + ageMax +
                ", ageDesc='" + ageDesc + '\'' +
                ", gradeLevel=" + gradeLevel +
                ", useScore=" + useScore +
                ", categoryId=" + categoryId +
                ", catId=" + catId +
                ", incomeId=" + incomeId +
                ", cityId=" + cityId +
                ", salesCityIds='" + salesCityIds + '\'' +
                ", businessType=" + businessType +
                ", provincesId=" + provincesId +
                ", areaId=" + areaId +
                ", districtId=" + districtId +
                ", businessId=" + businessId +
                ", destinationProvincesIds='" + destinationProvincesIds + '\'' +
                ", destinationCityIds='" + destinationCityIds + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", address='" + address + '\'' +
                ", peopleNum=" + peopleNum +
                ", price='" + price + '\'' +
                ", sellNum=" + sellNum +
                ", adminUid=" + adminUid +
                ", creatorUid=" + creatorUid +
                ", isConfirm=" + isConfirm +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", applyStartTime=" + applyStartTime +
                ", applyEndTime=" + applyEndTime +
                ", manager='" + manager + '\'' +
                ", managerMobile='" + managerMobile + '\'' +
                ", recommend='" + recommend + '\'' +
                ", isApply=" + isApply +
                ", isSell=" + isSell +
                ", applyScore=" + applyScore +
                ", sellStat=" + sellStat +
                ", isCreditExchange=" + isCreditExchange +
                ", exchangeScore=" + exchangeScore +
                ", exchangeNum=" + exchangeNum +
                ", sellMinPrice=" + sellMinPrice +
                ", marketMinPrice=" + marketMinPrice +
                ", listorder=" + listorder +
                ", clickNum=" + clickNum +
                ", status='" + status + '\'' +
                ", partnerStatus=" + partnerStatus +
                ", partnerCreateStatus=" + partnerCreateStatus +
                ", wxappShow=" + wxappShow +
                ", ticket='" + ticket + '\'' +
                ", relateAid='" + relateAid + '\'' +
                ", buyAid=" + buyAid +
                ", platId='" + platId + '\'' +
                ", editorUid=" + editorUid +
                ", marketUid=" + marketUid +
                ", linkUtime=" + linkUtime +
                ", utime=" + utime +
                ", publishTime=" + publishTime +
                ", ctime=" + ctime +
                '}';
    }
}
