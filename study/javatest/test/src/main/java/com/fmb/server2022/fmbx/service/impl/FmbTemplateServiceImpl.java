package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbTemplate;
import com.fmb.server2022.fmbx.mapper.FmbTemplateMapper;
import com.fmb.server2022.fmbx.service.IFmbTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 通用模块数据存储 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-09
 */
@Service
public class FmbTemplateServiceImpl extends ServiceImpl<FmbTemplateMapper, FmbTemplate> implements IFmbTemplateService {

}
