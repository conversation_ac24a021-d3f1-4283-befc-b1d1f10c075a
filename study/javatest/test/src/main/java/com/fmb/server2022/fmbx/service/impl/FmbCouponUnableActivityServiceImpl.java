package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbCouponUnableActivity;
import com.fmb.server2022.fmbx.mapper.FmbCouponUnableActivityMapper;
import com.fmb.server2022.fmbx.service.IFmbCouponUnableActivityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 禁用优惠券产品名单 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-09
 */
@Service
public class FmbCouponUnableActivityServiceImpl extends ServiceImpl<FmbCouponUnableActivityMapper, FmbCouponUnableActivity> implements IFmbCouponUnableActivityService {

}
