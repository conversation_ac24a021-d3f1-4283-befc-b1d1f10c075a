package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * fmbx活动主表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_order_stat")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxOrderStat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "stat_id", type = IdType.AUTO)
    private Integer statId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 订单用户uid
     */
    private Integer uid;

    /**
     * 分销商id
     */
    private Integer mycpsId;

    /**
     * 订单状态:0-未付款,1-等待发货,2-已发货,3-退货中,4-已退货,5-交易成功,6-交易关闭,7-支付尾款,8-支付成功后续处理中
     */
    private Integer orderStatus;

    /**
     * 商品配送情况:0-未发货,1-已发货,2-已收货,3-备货中
     */
    private Integer shippingStatus;

    /**
     * 支付状态:0-未付款,1-付款中,2-已付款
     */
    private Integer payStatus;

    /**
     * 二次确认状态:0-待处理,1-处理中,5-处理中(预订),8-处理中(预定催单),6-处理中(更改),9-处理中(更改催单),7-处理中(取消),10-处理中(取消催单),2-有货保留,3-无货待处理,4-无货退款
     */
    private Integer confirmStatus;

    /**
     * 二次确认复审状态:0-未审,1-已审
     */
    private Integer confirmRecheckStatus;

    /**
     * 结算状态:0-未结算,1-已经结算
     */
    private Integer payClear;

    /**
     * 关联活动ID
     */
    private Integer aid;

    /**
     * 订单所属区域ID
     */
    private Integer orderCityId;

    /**
     * 对应的商家管理ID
     */
    private Integer shopUserId;

    /**
     * 票务模板:1-演出,2-景点,3-通用,4-酒店,6-新酒店日历房,7-新酒店房券,15-酒+景,50-跟团游,51-自由行
     */
    private Integer goodsType;

    /**
     * 订单类型:1-普通,2-酒店预约单,3-返佣模式订单,4-分销价模式订单,10-知行酒店,11-六艺通
     */
    private Integer orderType;

    /**
     * 订单改签类型:1-后台,2-前台用户
     */
    private Integer changeType;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 收货人手机
     */
    private String mobile;

    /**
     * 货物金额
     */
    private BigDecimal money;

    /**
     * 配送费用
     */
    private BigDecimal shippingFee;

    /**
     * 订单来源页面
     */
    private String referer;

    /**
     * APP名字
     */
    private String appname;

    /**
     * 订单生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 订单确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime confirmTime;

    /**
     * 订单支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    /**
     * 二次确认操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime confirmUpdateTime;

    /**
     * 是否使用了优惠券:0-为未使用,1-使用
     */
    private Integer useCoupon;

    /**
     * 优惠金额
     */
    private Integer couponMoney;

    /**
     * 账户余额扣款
     */
    private BigDecimal cashMoney;

    /**
     * 折扣券的折扣率
     */
    private Integer discountRate;

    /**
     * 实际支付金额
     */
    private BigDecimal realpayMoney;

    /**
     * cps第三方名称
     */
    private String cpsName;

    /**
     * 订单是否删除:0-未删除,1-前台用户删除,2-后台用户删除
     */
    private Integer isDelete;

    /**
     * 活动供应商id
     */
    private Integer bpId;

    /**
     * 活动合同id 多个英文逗号分隔
     */
    private String bpcId;

    /**
     * 产品分类id
     */
    private Integer categoryId;

    /**
     * 产品票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店
     */
    private Integer ticketType;

    /**
     * 产品活动标题
     */
    private String title;

    /**
     * 产品显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
     */
    private String platId;

    /**
     * 产品所属分站
     */
    private Integer cityId;

    /**
     * 活动所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
     */
    private Integer businessType;

    /**
     * 活动二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
     */
    private Integer isConfirm;

    /**
     * 活动创建人的uid
     */
    private Integer createUid;

    /**
     * 活动创建人的名字(冗余)
     */
    private String createUidName;

    /**
     * 订单生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate createDate;

    public Integer getStatId() {
        return statId;
    }

    public void setStatId(Integer statId) {
        this.statId = statId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getMycpsId() {
        return mycpsId;
    }

    public void setMycpsId(Integer mycpsId) {
        this.mycpsId = mycpsId;
    }
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }
    public Integer getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(Integer shippingStatus) {
        this.shippingStatus = shippingStatus;
    }
    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }
    public Integer getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(Integer confirmStatus) {
        this.confirmStatus = confirmStatus;
    }
    public Integer getConfirmRecheckStatus() {
        return confirmRecheckStatus;
    }

    public void setConfirmRecheckStatus(Integer confirmRecheckStatus) {
        this.confirmRecheckStatus = confirmRecheckStatus;
    }
    public Integer getPayClear() {
        return payClear;
    }

    public void setPayClear(Integer payClear) {
        this.payClear = payClear;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getOrderCityId() {
        return orderCityId;
    }

    public void setOrderCityId(Integer orderCityId) {
        this.orderCityId = orderCityId;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }
    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }
    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(LocalDateTime confirmTime) {
        this.confirmTime = confirmTime;
    }
    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }
    public LocalDateTime getConfirmUpdateTime() {
        return confirmUpdateTime;
    }

    public void setConfirmUpdateTime(LocalDateTime confirmUpdateTime) {
        this.confirmUpdateTime = confirmUpdateTime;
    }
    public Integer getUseCoupon() {
        return useCoupon;
    }

    public void setUseCoupon(Integer useCoupon) {
        this.useCoupon = useCoupon;
    }
    public Integer getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(Integer couponMoney) {
        this.couponMoney = couponMoney;
    }
    public BigDecimal getCashMoney() {
        return cashMoney;
    }

    public void setCashMoney(BigDecimal cashMoney) {
        this.cashMoney = cashMoney;
    }
    public Integer getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Integer discountRate) {
        this.discountRate = discountRate;
    }
    public BigDecimal getRealpayMoney() {
        return realpayMoney;
    }

    public void setRealpayMoney(BigDecimal realpayMoney) {
        this.realpayMoney = realpayMoney;
    }
    public String getCpsName() {
        return cpsName;
    }

    public void setCpsName(String cpsName) {
        this.cpsName = cpsName;
    }
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public String getBpcId() {
        return bpcId;
    }

    public void setBpcId(String bpcId) {
        this.bpcId = bpcId;
    }
    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
    public Integer getTicketType() {
        return ticketType;
    }

    public void setTicketType(Integer ticketType) {
        this.ticketType = ticketType;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public String getCreateUidName() {
        return createUidName;
    }

    public void setCreateUidName(String createUidName) {
        this.createUidName = createUidName;
    }
    public LocalDate getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDate createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "FmbxOrderStat{" +
            "statId=" + statId +
            ", orderSn=" + orderSn +
            ", orderName=" + orderName +
            ", uid=" + uid +
            ", mycpsId=" + mycpsId +
            ", orderStatus=" + orderStatus +
            ", shippingStatus=" + shippingStatus +
            ", payStatus=" + payStatus +
            ", confirmStatus=" + confirmStatus +
            ", confirmRecheckStatus=" + confirmRecheckStatus +
            ", payClear=" + payClear +
            ", aid=" + aid +
            ", orderCityId=" + orderCityId +
            ", shopUserId=" + shopUserId +
            ", goodsType=" + goodsType +
            ", orderType=" + orderType +
            ", changeType=" + changeType +
            ", receiver=" + receiver +
            ", mobile=" + mobile +
            ", money=" + money +
            ", shippingFee=" + shippingFee +
            ", referer=" + referer +
            ", appname=" + appname +
            ", createTime=" + createTime +
            ", confirmTime=" + confirmTime +
            ", payTime=" + payTime +
            ", confirmUpdateTime=" + confirmUpdateTime +
            ", useCoupon=" + useCoupon +
            ", couponMoney=" + couponMoney +
            ", cashMoney=" + cashMoney +
            ", discountRate=" + discountRate +
            ", realpayMoney=" + realpayMoney +
            ", cpsName=" + cpsName +
            ", isDelete=" + isDelete +
            ", bpId=" + bpId +
            ", bpcId=" + bpcId +
            ", categoryId=" + categoryId +
            ", ticketType=" + ticketType +
            ", title=" + title +
            ", platId=" + platId +
            ", cityId=" + cityId +
            ", businessType=" + businessType +
            ", isConfirm=" + isConfirm +
            ", createUid=" + createUid +
            ", createUidName=" + createUidName +
            ", createDate=" + createDate +
        "}";
    }
}
