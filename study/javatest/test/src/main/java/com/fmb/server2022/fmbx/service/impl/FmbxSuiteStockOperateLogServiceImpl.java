package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuiteStockOperateLog;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteStockOperateLogMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockOperateLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店房型库存修改日志 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-17
 */
@Service
public class FmbxSuiteStockOperateLogServiceImpl extends ServiceImpl<FmbxSuiteStockOperateLogMapper, FmbxSuiteStockOperateLog> implements IFmbxSuiteStockOperateLogService {

}
