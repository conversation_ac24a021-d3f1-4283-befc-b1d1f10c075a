package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.mapper.FmbUsersMapper;
import com.fmb.server2022.fmbx.service.IFmbUsersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 用户信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-09
 */
@Service
public class FmbUsersServiceImpl extends ServiceImpl<FmbUsersMapper, FmbUsers> implements IFmbUsersService {

}
