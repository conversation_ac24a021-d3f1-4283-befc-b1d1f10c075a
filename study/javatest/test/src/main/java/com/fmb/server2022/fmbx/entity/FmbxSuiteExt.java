package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 套餐扩展表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_ext")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuiteExt implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "suite_id", type = IdType.ASSIGN_ID)
    private Integer suiteId;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 购买须知
     */
    private String buyNote;

    /**
     * 支付成功短信
     */
    private String smsPaySuccess;

    /**
     * 预约成功短信
     */
    private String smsResSuccess;

    /**
     * 二次确认短信
     */
    private String smsSecondConfirm;

    /**
     * 改签短信
     */
    private String smsReorder;



    /**
     * 订房单备注
     */
    private String roomOrderNote;

    /**
     * 内部备注
     */
    private String interiorNote;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public String getBuyNote() {
        return buyNote;
    }

    public void setBuyNote(String buyNote) {
        this.buyNote = buyNote;
    }
    public String getSmsPaySuccess() {
        return smsPaySuccess;
    }

    public void setSmsPaySuccess(String smsPaySuccess) {
        this.smsPaySuccess = smsPaySuccess;
    }
    public String getSmsSecondConfirm() {
        return smsSecondConfirm;
    }

    public void setSmsSecondConfirm(String smsSecondConfirm) {
        this.smsSecondConfirm = smsSecondConfirm;
    }
    public String getSmsReorder() {
        return smsReorder;
    }

    public void setSmsReorder(String smsReorder) {
        this.smsReorder = smsReorder;
    }
    public String getSmsResSuccess() {
        return smsResSuccess;
    }

    public void setSmsResSuccess(String smsResSuccess) {
        this.smsResSuccess = smsResSuccess;
    }
    public String getRoomOrderNote() {
        return roomOrderNote;
    }

    public void setRoomOrderNote(String roomOrderNote) {
        this.roomOrderNote = roomOrderNote;
    }
    public String getInteriorNote() {
        return interiorNote;
    }

    public void setInteriorNote(String interiorNote) {
        this.interiorNote = interiorNote;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }



    @Override
    public String toString() {
        return "FmbxSuiteExt{" +
            "suiteId=" + suiteId +
            ", versionNum=" + versionNum +
            ", buyNote=" + buyNote +
            ", smsPaySuccess=" + smsPaySuccess +
            ", smsSecondConfirm=" + smsSecondConfirm +
            ", smsReorder=" + smsReorder +
            ", smsResSuccess=" + smsResSuccess +
            ", roomOrderNote=" + roomOrderNote +
            ", interiorNote=" + interiorNote +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
