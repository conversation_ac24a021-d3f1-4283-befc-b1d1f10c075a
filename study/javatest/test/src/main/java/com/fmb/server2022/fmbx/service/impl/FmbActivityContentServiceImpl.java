package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbActivityContent;
import com.fmb.server2022.fmbx.mapper.FmbActivityContentMapper;
import com.fmb.server2022.fmbx.service.IFmbActivityContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 活动更多信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-25
 */
@Service
public class FmbActivityContentServiceImpl extends ServiceImpl<FmbActivityContentMapper, FmbActivityContent> implements IFmbActivityContentService {

}
