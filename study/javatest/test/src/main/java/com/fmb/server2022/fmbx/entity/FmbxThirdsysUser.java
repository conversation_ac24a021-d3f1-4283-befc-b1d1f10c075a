package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 三方系统用户信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_thirdsys_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxThirdsysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "tuser_id", type = IdType.AUTO)
    private Integer tuserId;

    /**
     * 三方用户id
     */
    private String thirdSysUid;

    /**
     * 三方用户姓名
     */
    private String sysUsername;

    /**
     * 三方部门id
     */
    private String thirdSysDepid;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 三方平台名称
     */
    private String sysName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 我方后台用户uid
     */
    private Integer adminUid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 是否在职:1-在职,0-不在职
     */
    private Integer status;

    public Integer getTuserId() {
        return tuserId;
    }

    public void setTuserId(Integer tuserId) {
        this.tuserId = tuserId;
    }
    public String getThirdSysUid() {
        return thirdSysUid;
    }

    public void setThirdSysUid(String thirdSysUid) {
        this.thirdSysUid = thirdSysUid;
    }
    public String getSysUsername() {
        return sysUsername;
    }

    public void setSysUsername(String sysUsername) {
        this.sysUsername = sysUsername;
    }
    public String getThirdSysDepid() {
        return thirdSysDepid;
    }

    public void setThirdSysDepid(String thirdSysDepid) {
        this.thirdSysDepid = thirdSysDepid;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getSysName() {
        return sysName;
    }

    public void setSysName(String sysName) {
        this.sysName = sysName;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "FmbxThirdsysUser{" +
            "tuserId=" + tuserId +
            ", thirdSysUid=" + thirdSysUid +
            ", sysUsername=" + sysUsername +
            ", thirdSysDepid=" + thirdSysDepid +
            ", email=" + email +
            ", sysName=" + sysName +
            ", mobile=" + mobile +
            ", adminUid=" + adminUid +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", status=" + status +
        "}";
    }
}
