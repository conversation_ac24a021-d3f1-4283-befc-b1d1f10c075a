package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbHotelHouseTypes;
import com.fmb.server2022.fmbx.mapper.FmbHotelHouseTypesMapper;
import com.fmb.server2022.fmbx.service.IFmbHotelHouseTypesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店产品的房型列表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-24
 */
@Service
public class FmbHotelHouseTypesServiceImpl extends ServiceImpl<FmbHotelHouseTypesMapper, FmbHotelHouseTypes> implements IFmbHotelHouseTypesService {

}
