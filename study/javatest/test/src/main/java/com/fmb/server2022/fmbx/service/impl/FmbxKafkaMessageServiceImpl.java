package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxKafkaMessage;
import com.fmb.server2022.fmbx.mapper.FmbxKafkaMessageMapper;
import com.fmb.server2022.fmbx.service.IFmbxKafkaMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 有问题的kafka消息,需要重新消费或者优化 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-07
 */
@Service
public class FmbxKafkaMessageServiceImpl extends ServiceImpl<FmbxKafkaMessageMapper, FmbxKafkaMessage> implements IFmbxKafkaMessageService {

}
