package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 发送的短信记录
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_sms")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbSms implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 短信通道类型qixintong、dayu
     */
    private String channel;

    /**
     * 用于的程序模块
     */
    private String mtype;

    private String mLogId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 信息内容
     */
    private String message;

    /**
     * 短信发送原因
     */
    private String reason;

    /**
     * 发送结果 1:00
     */
    private String ret;

    /**
     * 发送IP
     */
    private String ip;

    /**
     * 发送短信人uid；0，系统；
     */
    private Integer adminUid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getMtype() {
        return mtype;
    }

    public void setMtype(String mtype) {
        this.mtype = mtype;
    }
    public String getmLogId() {
        return mLogId;
    }

    public void setmLogId(String mLogId) {
        this.mLogId = mLogId;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
    public String getRet() {
        return ret;
    }

    public void setRet(String ret) {
        this.ret = ret;
    }
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "FmbSms{" +
            "id=" + id +
            ", channel=" + channel +
            ", mtype=" + mtype +
            ", mLogId=" + mLogId +
            ", phone=" + phone +
            ", message=" + message +
            ", reason=" + reason +
            ", ret=" + ret +
            ", ip=" + ip +
            ", adminUid=" + adminUid +
            ", createTime=" + createTime +
        "}";
    }
}
