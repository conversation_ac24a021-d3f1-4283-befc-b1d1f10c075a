package main.java.com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 新旧产品信息整合搜索
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_product_index")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbProductIndex implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动/产品ID
     */
    @TableId
    private Integer apId;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动英文标题
     */
    private String englishTitle;

    /**
     * 活动副标题
     */
    private String subTitle;

    /**
     * 是否Fclub产品 0:非父母帮,1:父母帮
     */
    private Integer isFclub;

    /**
     * 1:普通活动;2:app专享活动;3:app专享价格活动
     */
    private Integer activityType;

    /**
     * 1,演出展览,2,景点门票,3,通用商品,4,酒店住宿,5,报名活动,6,展示活动
     */
    private Integer activityGroupType;

    /**
     * 类型名称  base_product ->type_name
     */
    private String typeName;

    /**
     * 活动banner图  base_product ->banner 转string
     */
    private String banner;

    /**
     * 最小年龄
     */
    private Integer ageMin;

    /**
     * 最大年龄
     */
    private Integer ageMax;

    /**
     * 票务分类  base_product ->cat_id 
     */
    private Integer catId;

    /**
     * 城市ID(fmb_city) base_product->start_city_ids  目前仅单个出发城市
     */
    private Integer cityId;

    /**
     * 省市ID
     */
    private Integer provincesId;

    /**
     * 区域id(fmb_area)
     */
    private Integer areaId;

    /**
     * 非直辖市的区ID
     */
    private Integer districtId;

    /**
     * 可售卖分站
     */
    private String salesCityIds;

    /**
     * 活动(产品)所属业务线：1,(弃用)老长线;2,酒店;3,北京活动票务;5,上海活动票务;7,(废弃)其他;11-北京长线;12-上海长线
     */
    private Integer businessType;

    /**
     * 目的地省市IDS
     */
    private String destinationProvincesIds;

    /**
     * 目的地城市IDS
     */
    private String destinationCityIds;

    /**
     * 位置经度
     */
    private BigDecimal longitude;

    /**
     * 位置纬度
     */
    private BigDecimal latitude;

    /**
     * 活动管理者UID
     */
    private Integer adminUid;

    /**
     * 活动创建者UID
     */
    private Integer creatorUid;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 全局开始售卖时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startSellTime;

    /**
     * 全局停售时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endSellTime;

    /**
     * 活动报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyEndTime;

    /**
     * 0默认，不推荐，1为推荐
     */
    private String recommend;

    /**
     * 0-其它商家的；其它-自己的仓库
     */
    private Integer shopWarehouseId;

    /**
     * 第三方 
     */
    private String thirdPart;

    /**
     * 是否需要证件 0，不需要身份证，1，一证多票 2, 一证一票
     */
    private Integer isCert;

    /**
     * 0:未知或非卖品,1:关闭,2:无库存,4:已过期,8:已屏蔽,16:在售中  base_product->sell_state
     */
    private Integer sellStat;

    /**
     * 活动列表排序 base_product->sort_rank
     */
    private Integer listorder;

    /**
     * 显示平台：0,全平台；1,h5;2,web;3,app;9,全部不展示;10,小程序
     */
    private String platId;

    /**
     * 购票链接
     */
    private String ticket;

    /**
     * 点击数量
     */
    private Integer clickNum;

    /**
     * 卖出活动票的张数
     */
    private Integer sellNum;

    /**
     * 参与人数
     */
    private Integer peopleNum;

    /**
     * 状态,0:待启用,1:启用,2:停用 从new_activity导出时需要转换
     */
    private Integer status;

    /**
     * 该活动是否有有效的结伴游套餐0-无1-有
     */
    private Integer partnerStatus;

    /**
     * 小程序是否展示此活动 -- 1：展示 2：不展示
     */
    private Integer wxappShow;

    /**
     * 最低售价
     */
    private Float sellMinPrice;

    /**
     * 最低市场价格
     */
    private Float marketMinPrice;

    /**
     * 新上架时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime onShelvesTime;

    /**
     * 活动报名开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyStartTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * fmbx活动倒计时结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime fmbxEndTime;

    /**
     * fmbx活动标记
     */
    private Integer fmbxActivityFlag;

    /**
     * 酒店活动星级:1-豪华型,2-高档型,3-舒适型
     */
    private Integer fmbxHotelStarLevel;

    /**
     * fmbx活动有日历房标记:1-有,0-无
     */
    private Integer fmbxFlagHotelDate;

    /**
     * fmbx活动房券标记:1-有,0-无
     */
    private Integer fmbxFlagHotelReserve;

    /**
     * 我司市场对接人
     */
    private Integer marketUid;

    public Integer getApId() {
        return apId;
    }

    public void setApId(Integer apId) {
        this.apId = apId;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getEnglishTitle() {
        return englishTitle;
    }

    public void setEnglishTitle(String englishTitle) {
        this.englishTitle = englishTitle;
    }
    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
    public Integer getIsFclub() {
        return isFclub;
    }

    public void setIsFclub(Integer isFclub) {
        this.isFclub = isFclub;
    }
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    public Integer getActivityGroupType() {
        return activityGroupType;
    }

    public void setActivityGroupType(Integer activityGroupType) {
        this.activityGroupType = activityGroupType;
    }
    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public Integer getAgeMin() {
        return ageMin;
    }

    public void setAgeMin(Integer ageMin) {
        this.ageMin = ageMin;
    }
    public Integer getAgeMax() {
        return ageMax;
    }

    public void setAgeMax(Integer ageMax) {
        this.ageMax = ageMax;
    }
    public Integer getCatId() {
        return catId;
    }

    public void setCatId(Integer catId) {
        this.catId = catId;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public Integer getProvincesId() {
        return provincesId;
    }

    public void setProvincesId(Integer provincesId) {
        this.provincesId = provincesId;
    }
    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }
    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }
    public String getSalesCityIds() {
        return salesCityIds;
    }

    public void setSalesCityIds(String salesCityIds) {
        this.salesCityIds = salesCityIds;
    }
    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
    public String getDestinationProvincesIds() {
        return destinationProvincesIds;
    }

    public void setDestinationProvincesIds(String destinationProvincesIds) {
        this.destinationProvincesIds = destinationProvincesIds;
    }
    public String getDestinationCityIds() {
        return destinationCityIds;
    }

    public void setDestinationCityIds(String destinationCityIds) {
        this.destinationCityIds = destinationCityIds;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getCreatorUid() {
        return creatorUid;
    }

    public void setCreatorUid(Integer creatorUid) {
        this.creatorUid = creatorUid;
    }
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public LocalDateTime getStartSellTime() {
        return startSellTime;
    }

    public void setStartSellTime(LocalDateTime startSellTime) {
        this.startSellTime = startSellTime;
    }
    public LocalDateTime getEndSellTime() {
        return endSellTime;
    }

    public void setEndSellTime(LocalDateTime endSellTime) {
        this.endSellTime = endSellTime;
    }
    public LocalDateTime getApplyEndTime() {
        return applyEndTime;
    }

    public void setApplyEndTime(LocalDateTime applyEndTime) {
        this.applyEndTime = applyEndTime;
    }
    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }
    public Integer getShopWarehouseId() {
        return shopWarehouseId;
    }

    public void setShopWarehouseId(Integer shopWarehouseId) {
        this.shopWarehouseId = shopWarehouseId;
    }
    public String getThirdPart() {
        return thirdPart;
    }

    public void setThirdPart(String thirdPart) {
        this.thirdPart = thirdPart;
    }
    public Integer getIsCert() {
        return isCert;
    }

    public void setIsCert(Integer isCert) {
        this.isCert = isCert;
    }
    public Integer getSellStat() {
        return sellStat;
    }

    public void setSellStat(Integer sellStat) {
        this.sellStat = sellStat;
    }
    public Integer getListorder() {
        return listorder;
    }

    public void setListorder(Integer listorder) {
        this.listorder = listorder;
    }
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }
    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
    public Integer getClickNum() {
        return clickNum;
    }

    public void setClickNum(Integer clickNum) {
        this.clickNum = clickNum;
    }
    public Integer getSellNum() {
        return sellNum;
    }

    public void setSellNum(Integer sellNum) {
        this.sellNum = sellNum;
    }
    public Integer getPeopleNum() {
        return peopleNum;
    }

    public void setPeopleNum(Integer peopleNum) {
        this.peopleNum = peopleNum;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Integer partnerStatus) {
        this.partnerStatus = partnerStatus;
    }
    public Integer getWxappShow() {
        return wxappShow;
    }

    public void setWxappShow(Integer wxappShow) {
        this.wxappShow = wxappShow;
    }
    public Float getSellMinPrice() {
        return sellMinPrice;
    }

    public void setSellMinPrice(Float sellMinPrice) {
        this.sellMinPrice = sellMinPrice;
    }
    public Float getMarketMinPrice() {
        return marketMinPrice;
    }

    public void setMarketMinPrice(Float marketMinPrice) {
        this.marketMinPrice = marketMinPrice;
    }
    public LocalDateTime getOnShelvesTime() {
        return onShelvesTime;
    }

    public void setOnShelvesTime(LocalDateTime onShelvesTime) {
        this.onShelvesTime = onShelvesTime;
    }
    public LocalDateTime getApplyStartTime() {
        return applyStartTime;
    }

    public void setApplyStartTime(LocalDateTime applyStartTime) {
        this.applyStartTime = applyStartTime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getFmbxEndTime() {
        return fmbxEndTime;
    }

    public void setFmbxEndTime(LocalDateTime fmbxEndTime) {
        this.fmbxEndTime = fmbxEndTime;
    }
    public Integer getFmbxActivityFlag() {
        return fmbxActivityFlag;
    }

    public void setFmbxActivityFlag(Integer fmbxActivityFlag) {
        this.fmbxActivityFlag = fmbxActivityFlag;
    }
    public Integer getFmbxHotelStarLevel() {
        return fmbxHotelStarLevel;
    }

    public void setFmbxHotelStarLevel(Integer fmbxHotelStarLevel) {
        this.fmbxHotelStarLevel = fmbxHotelStarLevel;
    }
    public Integer getFmbxFlagHotelDate() {
        return fmbxFlagHotelDate;
    }

    public void setFmbxFlagHotelDate(Integer fmbxFlagHotelDate) {
        this.fmbxFlagHotelDate = fmbxFlagHotelDate;
    }
    public Integer getFmbxFlagHotelReserve() {
        return fmbxFlagHotelReserve;
    }

    public void setFmbxFlagHotelReserve(Integer fmbxFlagHotelReserve) {
        this.fmbxFlagHotelReserve = fmbxFlagHotelReserve;
    }
    public Integer getMarketUid() {
        return marketUid;
    }

    public void setMarketUid(Integer marketUid) {
        this.marketUid = marketUid;
    }

    @Override
    public String toString() {
        return "FmbProductIndex{" +
            "apId=" + apId +
            ", title=" + title +
            ", englishTitle=" + englishTitle +
            ", subTitle=" + subTitle +
            ", isFclub=" + isFclub +
            ", activityType=" + activityType +
            ", activityGroupType=" + activityGroupType +
            ", typeName=" + typeName +
            ", banner=" + banner +
            ", ageMin=" + ageMin +
            ", ageMax=" + ageMax +
            ", catId=" + catId +
            ", cityId=" + cityId +
            ", provincesId=" + provincesId +
            ", areaId=" + areaId +
            ", districtId=" + districtId +
            ", salesCityIds=" + salesCityIds +
            ", businessType=" + businessType +
            ", destinationProvincesIds=" + destinationProvincesIds +
            ", destinationCityIds=" + destinationCityIds +
            ", longitude=" + longitude +
            ", latitude=" + latitude +
            ", adminUid=" + adminUid +
            ", creatorUid=" + creatorUid +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", startSellTime=" + startSellTime +
            ", endSellTime=" + endSellTime +
            ", applyEndTime=" + applyEndTime +
            ", recommend=" + recommend +
            ", shopWarehouseId=" + shopWarehouseId +
            ", thirdPart=" + thirdPart +
            ", isCert=" + isCert +
            ", sellStat=" + sellStat +
            ", listorder=" + listorder +
            ", platId=" + platId +
            ", ticket=" + ticket +
            ", clickNum=" + clickNum +
            ", sellNum=" + sellNum +
            ", peopleNum=" + peopleNum +
            ", status=" + status +
            ", partnerStatus=" + partnerStatus +
            ", wxappShow=" + wxappShow +
            ", sellMinPrice=" + sellMinPrice +
            ", marketMinPrice=" + marketMinPrice +
            ", onShelvesTime=" + onShelvesTime +
            ", applyStartTime=" + applyStartTime +
            ", utime=" + utime +
            ", ctime=" + ctime +
            ", fmbxEndTime=" + fmbxEndTime +
            ", fmbxActivityFlag=" + fmbxActivityFlag +
            ", fmbxHotelStarLevel=" + fmbxHotelStarLevel +
            ", fmbxFlagHotelDate=" + fmbxFlagHotelDate +
            ", fmbxFlagHotelReserve=" + fmbxFlagHotelReserve +
            ", marketUid=" + marketUid +
        "}";
    }
}
