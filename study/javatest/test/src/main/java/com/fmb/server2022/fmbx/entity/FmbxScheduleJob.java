package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 定时任务执行
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_schedule_job")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxScheduleJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "job_id", type = IdType.AUTO)
    private Integer jobId;

    /**
     * 任务类型
     */
    private String jobType;

    /**
     * 业务id
     */
    private Integer jobFmbxId;

    /**
     * 其他执行参数,json
     */
    private String jobExtPars;

    /**
     * 任务状态:0-新建等待执行,1-取消,2-执行完成,3-执行完成有异常
     */
    private Integer status;

    /**
     * 执行结果
     */
    private String result;

    /**
     * 预计执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime scheduleRunTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }
    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }
    public Integer getJobFmbxId() {
        return jobFmbxId;
    }

    public void setJobFmbxId(Integer jobFmbxId) {
        this.jobFmbxId = jobFmbxId;
    }
    public String getJobExtPars() {
        return jobExtPars;
    }

    public void setJobExtPars(String jobExtPars) {
        this.jobExtPars = jobExtPars;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
    public LocalDateTime getScheduleRunTime() {
        return scheduleRunTime;
    }

    public void setScheduleRunTime(LocalDateTime scheduleRunTime) {
        this.scheduleRunTime = scheduleRunTime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxScheduleJob{" +
            "jobId=" + jobId +
            ", jobType=" + jobType +
            ", jobFmbxId=" + jobFmbxId +
            ", jobExtPars=" + jobExtPars +
            ", status=" + status +
            ", result=" + result +
            ", scheduleRunTime=" + scheduleRunTime +
            ", utime=" + utime +
            ", ctime=" + ctime +
        "}";
    }
}
