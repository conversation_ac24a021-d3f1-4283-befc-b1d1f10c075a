package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 支付订单
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_pay_orders")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbPayOrders implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pay_id", type = IdType.AUTO)
    private Integer payId;

    /**
     * 自己支付订单号
     */
    private String paySn;

    /**
     * 支付的关联订单号
     */
    private String moneyOrderSn;

    /**
     * 第三方支付平台交易号
     */
    private String tradeNo;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 返回IP
     */
    private String returnIp;

    /**
     * 通知IP
     */
    private String notifyIp;

    /**
     * 所有请求日志信息
     */
    private String requestText;

    /**
     * 所有响应日志信息
     */
    private String responseText;

    /**
     * 支付平台
     */
    private String payType;

    /**
     * 订单来源
     */
    private String referer;

    /**
     * 是否支付成功
     */
    private Integer isSuccess;

    /**
     * 成功支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime successTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getPayId() {
        return payId;
    }

    public void setPayId(Integer payId) {
        this.payId = payId;
    }
    public String getPaySn() {
        return paySn;
    }

    public void setPaySn(String paySn) {
        this.paySn = paySn;
    }
    public String getMoneyOrderSn() {
        return moneyOrderSn;
    }

    public void setMoneyOrderSn(String moneyOrderSn) {
        this.moneyOrderSn = moneyOrderSn;
    }
    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }
    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }
    public String getReturnIp() {
        return returnIp;
    }

    public void setReturnIp(String returnIp) {
        this.returnIp = returnIp;
    }
    public String getNotifyIp() {
        return notifyIp;
    }

    public void setNotifyIp(String notifyIp) {
        this.notifyIp = notifyIp;
    }
    public String getRequestText() {
        return requestText;
    }

    public void setRequestText(String requestText) {
        this.requestText = requestText;
    }
    public String getResponseText() {
        return responseText;
    }

    public void setResponseText(String responseText) {
        this.responseText = responseText;
    }
    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }
    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }
    public Integer getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Integer isSuccess) {
        this.isSuccess = isSuccess;
    }
    public LocalDateTime getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(LocalDateTime successTime) {
        this.successTime = successTime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbPayOrders{" +
            "payId=" + payId +
            ", paySn=" + paySn +
            ", moneyOrderSn=" + moneyOrderSn +
            ", tradeNo=" + tradeNo +
            ", requestIp=" + requestIp +
            ", returnIp=" + returnIp +
            ", notifyIp=" + notifyIp +
            ", requestText=" + requestText +
            ", responseText=" + responseText +
            ", payType=" + payType +
            ", referer=" + referer +
            ", isSuccess=" + isSuccess +
            ", successTime=" + successTime +
            ", ctime=" + ctime +
        "}";
    }
}
