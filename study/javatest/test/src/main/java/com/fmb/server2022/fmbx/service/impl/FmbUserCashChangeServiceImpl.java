package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbUserCashChange;
import com.fmb.server2022.fmbx.mapper.FmbUserCashChangeMapper;
import com.fmb.server2022.fmbx.service.IFmbUserCashChangeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 用户账户余额变动 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class FmbUserCashChangeServiceImpl extends ServiceImpl<FmbUserCashChangeMapper, FmbUserCashChange> implements IFmbUserCashChangeService {

}
