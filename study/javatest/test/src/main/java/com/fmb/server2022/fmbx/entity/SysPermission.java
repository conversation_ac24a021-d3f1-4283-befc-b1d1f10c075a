package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <p>
 * 后台权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@TableName("sys_permission")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自定id,主要供前端展示权限列表分类排序使用.
     */
    @TableId(value = "id", type = IdType.AUTO)

    private Integer id;

    /**
     * 归属菜单,前端判断并展示菜单使用,
     */

    private String menuCode;

    /**
     * 菜单的中文释义
     */

    private String menuName;

    /**
     * 权限的代码/通配符,对应代码中@RequiresPermissions 的value
     */

    private String permissionCode;

    /**
     * 本权限的中文释义
     */

    private String permissionName;

    /**
     * 是否本菜单必选权限:1=必选(是列表),2=非必选
     */

    private Integer requiredPermission;

    /**
     * 接口地址
     */

    private String apiuri;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public Integer getRequiredPermission() {
        return requiredPermission;
    }

    public void setRequiredPermission(Integer requiredPermission) {
        this.requiredPermission = requiredPermission;
    }

    public String getApiuri() {
        return apiuri;
    }

    public void setApiuri(String apiuri) {
        this.apiuri = apiuri;
    }

    @Override
    public String toString() {
        return "SysPermission{" +
                "id=" + id +
                ", menuCode=" + menuCode +
                ", menuName=" + menuName +
                ", permissionCode=" + permissionCode +
                ", permissionName=" + permissionName +
                ", requiredPermission=" + requiredPermission +
                ", apiuri=" + apiuri +
                "}";
    }
}
