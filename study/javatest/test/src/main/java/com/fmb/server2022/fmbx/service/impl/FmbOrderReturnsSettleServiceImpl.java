package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderReturnsSettle;
import com.fmb.server2022.fmbx.mapper.FmbOrderReturnsSettleMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsSettleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 退货单的已经结算信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class FmbOrderReturnsSettleServiceImpl extends ServiceImpl<FmbOrderReturnsSettleMapper, FmbOrderReturnsSettle> implements IFmbOrderReturnsSettleService {

}
