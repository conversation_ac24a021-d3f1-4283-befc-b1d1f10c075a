package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商主表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_main")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpMain implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bp_id", type = IdType.AUTO)
    private Integer bpId;

    /**
     * 商家类型:1-酒店,2-其他
     */
    private Integer parterType;

    /**
     * 账号名字
     */
    private String accountName;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 盐值
     */
    private String salt;

    /**
     * 我司对接人,后台用户uid
     */
    private Integer adminUid;

    /**
     * 我司对接人姓名
     */
    private String adminName;

    /**
     * 我司对接人手机号
     */
    private String adminMobile;

    /**
     * 我司对接人邮箱
     */
    private String adminEmail;

    /**
     * 我司对接人地区(北京,上海)
     */
    private String adminCity;

    /**
     * 结算周期,每月几号
     */
    private Integer settleEachMonthday;

    /**
     * 结算发起方式:1-系统自动,2-结算人员手动发起
     */
    private Integer settleGenType;

    /**
     * 结算方式:1-下单时间,2-消费时间,3-入住时间
     */
    private Integer settleType;

    /**
     * 发票类型:1-结算发票,2-佣金发票
     */
    private Integer invoiceType;

    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 账号类型:1-主账号,2-子账号
     */
    private Integer accountType;

    /**
     * 账号类型:1-正常,0-禁用
     */
    private Integer accountStatus;

    /**
     * 审核状态:1-待供应商完善,2-待商务审核,3-审核通过,4-审核拒绝
     */
    private Integer checkStatus;

    /**
     * 创建人后台用户uid
     */
    private Integer createUid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 商家后台展示用户手机号 1:显示 0：不显示
     */
    private Integer isShowMobile;

    /**
     * 商家后台展示用户姓名 1:不展示 0：展示
     */
    private Integer isShowCustomerName;

    /**
     * 展示销售数据 0否 1是 默认0
     */
    private Integer showOrderStat;

    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getParterType() {
        return parterType;
    }

    public void setParterType(Integer parterType) {
        this.parterType = parterType;
    }
    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }
    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }
    public String getAdminMobile() {
        return adminMobile;
    }

    public void setAdminMobile(String adminMobile) {
        this.adminMobile = adminMobile;
    }
    public String getAdminEmail() {
        return adminEmail;
    }

    public void setAdminEmail(String adminEmail) {
        this.adminEmail = adminEmail;
    }
    public String getAdminCity() {
        return adminCity;
    }

    public void setAdminCity(String adminCity) {
        this.adminCity = adminCity;
    }
    public Integer getSettleEachMonthday() {
        return settleEachMonthday;
    }

    public void setSettleEachMonthday(Integer settleEachMonthday) {
        this.settleEachMonthday = settleEachMonthday;
    }
    public Integer getSettleGenType() {
        return settleGenType;
    }

    public void setSettleGenType(Integer settleGenType) {
        this.settleGenType = settleGenType;
    }
    public Integer getSettleType() {
        return settleType;
    }

    public void setSettleType(Integer settleType) {
        this.settleType = settleType;
    }
    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }
    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }
    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }
    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }
    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getIsShowMobile() {
        return isShowMobile;
    }

    public void setIsShowMobile(Integer isShowMobile) {
        this.isShowMobile = isShowMobile;
    }
    public Integer getIsShowCustomerName() {
        return isShowCustomerName;
    }

    public void setIsShowCustomerName(Integer isShowCustomerName) {
        this.isShowCustomerName = isShowCustomerName;
    }
    public Integer getShowOrderStat() {
        return showOrderStat;
    }

    public void setShowOrderStat(Integer showOrderStat) {
        this.showOrderStat = showOrderStat;
    }

    @Override
    public String toString() {
        return "FmbxBpMain{" +
            "bpId=" + bpId +
            ", parterType=" + parterType +
            ", accountName=" + accountName +
            ", passwd=" + passwd +
            ", salt=" + salt +
            ", adminUid=" + adminUid +
            ", adminName=" + adminName +
            ", adminMobile=" + adminMobile +
            ", adminEmail=" + adminEmail +
            ", adminCity=" + adminCity +
            ", settleEachMonthday=" + settleEachMonthday +
            ", settleGenType=" + settleGenType +
            ", settleType=" + settleType +
            ", invoiceType=" + invoiceType +
            ", pid=" + pid +
            ", accountType=" + accountType +
            ", accountStatus=" + accountStatus +
            ", checkStatus=" + checkStatus +
            ", createUid=" + createUid +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", isShowMobile=" + isShowMobile +
            ", isShowCustomerName=" + isShowCustomerName +
            ", showOrderStat=" + showOrderStat +
        "}";
    }
}
