package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderSettleDetails;
import com.fmb.server2022.fmbx.mapper.FmbOrderSettleDetailsMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderSettleDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 结算明细表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class FmbOrderSettleDetailsServiceImpl extends ServiceImpl<FmbOrderSettleDetailsMapper, FmbOrderSettleDetails> implements IFmbOrderSettleDetailsService {

}
