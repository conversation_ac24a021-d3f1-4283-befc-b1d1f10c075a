package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店房型库存修改日志
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_stock_operate_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuiteStockOperateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "stock_oplog_id", type = IdType.AUTO)
    private Integer stockOplogId;

    /**
     * stock_operate_id
     */
    private Integer stockOperateId;

    /**
     * room_id
     */
    private Integer roomId;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate targetDate;

    /**
     * 具体操作信息
     */
    private String operateInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getStockOplogId() {
        return stockOplogId;
    }

    public void setStockOplogId(Integer stockOplogId) {
        this.stockOplogId = stockOplogId;
    }
    public Integer getStockOperateId() {
        return stockOperateId;
    }

    public void setStockOperateId(Integer stockOperateId) {
        this.stockOperateId = stockOperateId;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public LocalDate getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(LocalDate targetDate) {
        this.targetDate = targetDate;
    }
    public String getOperateInfo() {
        return operateInfo;
    }

    public void setOperateInfo(String operateInfo) {
        this.operateInfo = operateInfo;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxSuiteStockOperateLog{" +
            "stockOplogId=" + stockOplogId +
            ", stockOperateId=" + stockOperateId +
            ", roomId=" + roomId +
            ", targetDate=" + targetDate +
            ", operateInfo=" + operateInfo +
            ", ctime=" + ctime +
        "}";
    }
}
