package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 与订单关联的附加信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_related_data")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderRelatedData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 可联系我确认预约到店时间:0否,1是
     */
    private Integer callUser;

    /**
     * 通用id，如分享id
     */
    private Integer commonId;

    /**
     * 类型：1分享,2场馆类预定活动订单,3酒店电话类预订活动订单,4酒店线上预约活动订单,10普通订单
     */
    private Integer type;

    /**
     * 包含次数
     */
    private Integer containTimes;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 描述
     */
    @TableField(value = "`desc`")
    private String desc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 预约结束状态：0未结束,1已结束
     */
    private Integer reserveEndStatus;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getCallUser() {
        return callUser;
    }

    public void setCallUser(Integer callUser) {
        this.callUser = callUser;
    }
    public Integer getCommonId() {
        return commonId;
    }

    public void setCommonId(Integer commonId) {
        this.commonId = commonId;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public Integer getContainTimes() {
        return containTimes;
    }

    public void setContainTimes(Integer containTimes) {
        this.containTimes = containTimes;
    }
    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getReserveEndStatus() {
        return reserveEndStatus;
    }

    public void setReserveEndStatus(Integer reserveEndStatus) {
        this.reserveEndStatus = reserveEndStatus;
    }

    @Override
    public String toString() {
        return "FmbOrderRelatedData{" +
            "id=" + id +
            ", orderSn=" + orderSn +
            ", uid=" + uid +
            ", callUser=" + callUser +
            ", commonId=" + commonId +
            ", type=" + type +
            ", containTimes=" + containTimes +
            ", number=" + number +
            ", desc=" + desc +
            ", ctime=" + ctime +
            ", reserveEndStatus=" + reserveEndStatus +
        "}";
    }
}
