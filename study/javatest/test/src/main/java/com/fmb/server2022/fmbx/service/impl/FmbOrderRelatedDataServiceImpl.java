package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.mapper.FmbOrderRelatedDataMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderRelatedDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 与订单关联的附加信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-01
 */
@Service
public class FmbOrderRelatedDataServiceImpl extends ServiceImpl<FmbOrderRelatedDataMapper, FmbOrderRelatedData> implements IFmbOrderRelatedDataService {

}
