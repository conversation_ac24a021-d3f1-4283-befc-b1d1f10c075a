package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店预订表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_hotel")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderHotel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 保存之前的订单号
     */
    private String oldOrderSn;

    /**
     * 商品ID
     */
    private Integer recId;

    /**
     * 改签前的rec_id
     */
    private Integer lastRecId;

    /**
     * 票种ID
     */
    private Integer goodsId;

    /**
     * 表单样式0未知1新建2改签3变更4取消5其他 
     */
    private Integer formType;

    /**
     * 票种名称
     */
    private String ticketName;

    /**
     * 结算价
     */
    private String settlePrice;

    /**
     * 销售价
     */
    private String goodsPrice;

    /**
     * 房数
     */
    private Integer goodsNumber;

    /**
     * 房间数量
     */
    private Integer roomNum;

    /**
     * 房型
     */
    private String hotelType;

    /**
     * 用户姓名
     */
    private String receiver;

    /**
     * 用户联系方式
     */
    private String mobile;

    /**
     * 证件类型
     */
    private String cardType;

    /**
     * 证件号码
     */
    private String cardSn;

    private String postscript;

    /**
     * 父母邦备注
     */
    private String fmbComment;

    /**
     * 套餐备注
     */
    private String ticketComment;

    /**
     * 酒店名称
     */
    private String shopName;

    /**
     * 酒店联系电话
     */
    private String shopTel;

    /**
     * 酒店传真号码
     */
    private String shopFax;

    /**
     * 酒店确认人
     */
    private String confirmPeople;

    /**
     * 酒店确认号
     */
    private String confirmSn;

    /**
     * 后台操作uid
     */
    private Integer adminUid;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime playTime;

    /**
     * 离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime leaveTime;

    /**
     * 生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getOldOrderSn() {
        return oldOrderSn;
    }

    public void setOldOrderSn(String oldOrderSn) {
        this.oldOrderSn = oldOrderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public Integer getLastRecId() {
        return lastRecId;
    }

    public void setLastRecId(Integer lastRecId) {
        this.lastRecId = lastRecId;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getFormType() {
        return formType;
    }

    public void setFormType(Integer formType) {
        this.formType = formType;
    }
    public String getTicketName() {
        return ticketName;
    }

    public void setTicketName(String ticketName) {
        this.ticketName = ticketName;
    }
    public String getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(String settlePrice) {
        this.settlePrice = settlePrice;
    }
    public String getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(String goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public Integer getGoodsNumber() {
        return goodsNumber;
    }

    public void setGoodsNumber(Integer goodsNumber) {
        this.goodsNumber = goodsNumber;
    }
    public Integer getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(Integer roomNum) {
        this.roomNum = roomNum;
    }
    public String getHotelType() {
        return hotelType;
    }

    public void setHotelType(String hotelType) {
        this.hotelType = hotelType;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public String getCardSn() {
        return cardSn;
    }

    public void setCardSn(String cardSn) {
        this.cardSn = cardSn;
    }
    public String getPostscript() {
        return postscript;
    }

    public void setPostscript(String postscript) {
        this.postscript = postscript;
    }
    public String getFmbComment() {
        return fmbComment;
    }

    public void setFmbComment(String fmbComment) {
        this.fmbComment = fmbComment;
    }
    public String getTicketComment() {
        return ticketComment;
    }

    public void setTicketComment(String ticketComment) {
        this.ticketComment = ticketComment;
    }
    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
    public String getShopTel() {
        return shopTel;
    }

    public void setShopTel(String shopTel) {
        this.shopTel = shopTel;
    }
    public String getShopFax() {
        return shopFax;
    }

    public void setShopFax(String shopFax) {
        this.shopFax = shopFax;
    }
    public String getConfirmPeople() {
        return confirmPeople;
    }

    public void setConfirmPeople(String confirmPeople) {
        this.confirmPeople = confirmPeople;
    }
    public String getConfirmSn() {
        return confirmSn;
    }

    public void setConfirmSn(String confirmSn) {
        this.confirmSn = confirmSn;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getPlayTime() {
        return playTime;
    }

    public void setPlayTime(LocalDateTime playTime) {
        this.playTime = playTime;
    }
    public LocalDateTime getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(LocalDateTime leaveTime) {
        this.leaveTime = leaveTime;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "FmbOrderHotel{" +
            "id=" + id +
            ", orderSn=" + orderSn +
            ", oldOrderSn=" + oldOrderSn +
            ", recId=" + recId +
            ", lastRecId=" + lastRecId +
            ", goodsId=" + goodsId +
            ", formType=" + formType +
            ", ticketName=" + ticketName +
            ", settlePrice=" + settlePrice +
            ", goodsPrice=" + goodsPrice +
            ", goodsNumber=" + goodsNumber +
            ", roomNum=" + roomNum +
            ", hotelType=" + hotelType +
            ", receiver=" + receiver +
            ", mobile=" + mobile +
            ", cardType=" + cardType +
            ", cardSn=" + cardSn +
            ", postscript=" + postscript +
            ", fmbComment=" + fmbComment +
            ", ticketComment=" + ticketComment +
            ", shopName=" + shopName +
            ", shopTel=" + shopTel +
            ", shopFax=" + shopFax +
            ", confirmPeople=" + confirmPeople +
            ", confirmSn=" + confirmSn +
            ", adminUid=" + adminUid +
            ", playTime=" + playTime +
            ", leaveTime=" + leaveTime +
            ", createTime=" + createTime +
        "}";
    }
}
