package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.mapper.FmbxBpParterInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商商家信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-30
 */
@Service
public class FmbxBpParterInfoServiceImpl extends ServiceImpl<FmbxBpParterInfoMapper, FmbxBpParterInfo> implements IFmbxBpParterInfoService {

}
