package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 用户账户余额变动
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_user_cash_change")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbUserCashChange implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 余额流水日志id
     */
    private Integer cashlogId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 变动之前金额
     */
    private BigDecimal money;

    /**
     * 更新之后的金额
     */
    private BigDecimal newMoney;

    /**
     * 变更余额
     */
    private BigDecimal changeMoney;

    /**
     * 操作说明
     */
    private String note;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getCashlogId() {
        return cashlogId;
    }

    public void setCashlogId(Integer cashlogId) {
        this.cashlogId = cashlogId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getNewMoney() {
        return newMoney;
    }

    public void setNewMoney(BigDecimal newMoney) {
        this.newMoney = newMoney;
    }
    public BigDecimal getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(BigDecimal changeMoney) {
        this.changeMoney = changeMoney;
    }
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbUserCashChange{" +
            "id=" + id +
            ", uid=" + uid +
            ", cashlogId=" + cashlogId +
            ", orderSn=" + orderSn +
            ", money=" + money +
            ", newMoney=" + newMoney +
            ", changeMoney=" + changeMoney +
            ", note=" + note +
            ", ctime=" + ctime +
        "}";
    }
}
