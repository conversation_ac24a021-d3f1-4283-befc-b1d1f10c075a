package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商文件资源表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_media")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpMedia implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bpm_id", type = IdType.AUTO)
    private Integer bpmId;

    /**
     * 供应商ID
     */
    private Integer bpId;

    /**
     * 文件id
     */
    private Integer mediaId;

    /**
     * 文件的业务名称,如营业执照
     */
    private String fileTypeName;

    /**
     * 创建用户的id
     */
    private Integer createUserId;

    /**
     * 用户的类型:1-父母邦后台用户,2-商家后台用户
     */
    private Integer createUserType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 文件类型:1-营业执照,2-特殊经营
     */
    private Integer fileType;

    public Integer getBpmId() {
        return bpmId;
    }

    public void setBpmId(Integer bpmId) {
        this.bpmId = bpmId;
    }
    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getMediaId() {
        return mediaId;
    }

    public void setMediaId(Integer mediaId) {
        this.mediaId = mediaId;
    }
    public String getFileTypeName() {
        return fileTypeName;
    }

    public void setFileTypeName(String fileTypeName) {
        this.fileTypeName = fileTypeName;
    }
    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }
    public Integer getCreateUserType() {
        return createUserType;
    }

    public void setCreateUserType(Integer createUserType) {
        this.createUserType = createUserType;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }


    @Override
    public String toString() {
        return "FmbxBpMedia{" +
                "bpmId=" + bpmId +
                ", bpId=" + bpId +
                ", mediaId=" + mediaId +
                ", fileTypeName='" + fileTypeName + '\'' +
                ", createUserId=" + createUserId +
                ", createUserType=" + createUserType +
                ", ctime=" + ctime +
                ", utime=" + utime +
                ", fileType=" + fileType +
                '}';
    }
}
