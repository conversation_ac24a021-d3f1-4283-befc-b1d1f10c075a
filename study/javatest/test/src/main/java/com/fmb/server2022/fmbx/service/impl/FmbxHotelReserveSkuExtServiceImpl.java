package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSkuExt;
import com.fmb.server2022.fmbx.mapper.FmbxHotelReserveSkuExtMapper;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuExtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 房劵扩展表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-30
 */
@Service
public class FmbxHotelReserveSkuExtServiceImpl extends ServiceImpl<FmbxHotelReserveSkuExtMapper, FmbxHotelReserveSkuExt> implements IFmbxHotelReserveSkuExtService {

}
