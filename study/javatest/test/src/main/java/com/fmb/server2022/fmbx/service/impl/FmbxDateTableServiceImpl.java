package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxDateTable;
import com.fmb.server2022.fmbx.mapper.FmbxDateTableMapper;
import com.fmb.server2022.fmbx.service.IFmbxDateTableService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 节假日信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class FmbxDateTableServiceImpl extends ServiceImpl<FmbxDateTableMapper, FmbxDateTable> implements IFmbxDateTableService {

}
