package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxHotelOrderConfirm;
import com.fmb.server2022.fmbx.mapper.FmbxHotelOrderConfirmMapper;
import com.fmb.server2022.fmbx.service.IFmbxHotelOrderConfirmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * fmbx酒店二次确认表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class FmbxHotelOrderConfirmServiceImpl extends ServiceImpl<FmbxHotelOrderConfirmMapper, FmbxHotelOrderConfirm> implements IFmbxHotelOrderConfirmService {

}
