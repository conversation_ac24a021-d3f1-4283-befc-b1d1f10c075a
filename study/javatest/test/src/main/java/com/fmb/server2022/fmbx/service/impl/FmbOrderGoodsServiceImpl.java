package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.mapper.FmbOrderGoodsMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单对应的具体商品信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbOrderGoodsServiceImpl extends ServiceImpl<FmbOrderGoodsMapper, FmbOrderGoods> implements IFmbOrderGoodsService {

}
