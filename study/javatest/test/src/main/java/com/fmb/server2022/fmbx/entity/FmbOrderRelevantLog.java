package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单相关日志
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_relevant_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderRelevantLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "relevant_id", type = IdType.AUTO)
    private Integer relevantId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 与订单相关类 invoice,return,shipping,settlement...
     */
    private String orderRelevantType;

    /**
     * 订单相关类别对应各自的ID，如 invoice_id,return_id,ship
     */
    private Integer orderRelevantTypeId;

    /**
     * 类别相关各自的状态,如0未处理，1处理中，等
     */
    private Integer orderRelevantTypeStatus;

    /**
     * 日志内容
     */
    private String orderRelevantNote;

    /**
     * 标识符，如 order_returns_status,order_invoice_send,等
     */
    private String orderRelevantIdentifier;

    /**
     * 订单改签类型（目前长线在用）change_price：价格改签，change_date：日期改签
     */
    private String orderChangeType;

    /**
     * 后台用户uid
     */
    private Integer adminUid;

    /**
     * 日志生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime orderRelevantLogTime;

    public Integer getRelevantId() {
        return relevantId;
    }

    public void setRelevantId(Integer relevantId) {
        this.relevantId = relevantId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getOrderRelevantType() {
        return orderRelevantType;
    }

    public void setOrderRelevantType(String orderRelevantType) {
        this.orderRelevantType = orderRelevantType;
    }
    public Integer getOrderRelevantTypeId() {
        return orderRelevantTypeId;
    }

    public void setOrderRelevantTypeId(Integer orderRelevantTypeId) {
        this.orderRelevantTypeId = orderRelevantTypeId;
    }
    public Integer getOrderRelevantTypeStatus() {
        return orderRelevantTypeStatus;
    }

    public void setOrderRelevantTypeStatus(Integer orderRelevantTypeStatus) {
        this.orderRelevantTypeStatus = orderRelevantTypeStatus;
    }
    public String getOrderRelevantNote() {
        return orderRelevantNote;
    }

    public void setOrderRelevantNote(String orderRelevantNote) {
        this.orderRelevantNote = orderRelevantNote;
    }
    public String getOrderRelevantIdentifier() {
        return orderRelevantIdentifier;
    }

    public void setOrderRelevantIdentifier(String orderRelevantIdentifier) {
        this.orderRelevantIdentifier = orderRelevantIdentifier;
    }
    public String getOrderChangeType() {
        return orderChangeType;
    }

    public void setOrderChangeType(String orderChangeType) {
        this.orderChangeType = orderChangeType;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getOrderRelevantLogTime() {
        return orderRelevantLogTime;
    }

    public void setOrderRelevantLogTime(LocalDateTime orderRelevantLogTime) {
        this.orderRelevantLogTime = orderRelevantLogTime;
    }

    @Override
    public String toString() {
        return "FmbOrderRelevantLog{" +
            "relevantId=" + relevantId +
            ", orderSn=" + orderSn +
            ", orderRelevantType=" + orderRelevantType +
            ", orderRelevantTypeId=" + orderRelevantTypeId +
            ", orderRelevantTypeStatus=" + orderRelevantTypeStatus +
            ", orderRelevantNote=" + orderRelevantNote +
            ", orderRelevantIdentifier=" + orderRelevantIdentifier +
            ", orderChangeType=" + orderChangeType +
            ", adminUid=" + adminUid +
            ", orderRelevantLogTime=" + orderRelevantLogTime +
        "}";
    }
}
