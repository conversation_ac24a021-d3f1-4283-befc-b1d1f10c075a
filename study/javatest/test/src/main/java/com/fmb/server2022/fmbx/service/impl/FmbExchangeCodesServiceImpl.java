package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbExchangeCodes;
import com.fmb.server2022.fmbx.mapper.FmbExchangeCodesMapper;
import com.fmb.server2022.fmbx.service.IFmbExchangeCodesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 兑换码记录表，电子票 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-07
 */
@Service
public class FmbExchangeCodesServiceImpl extends ServiceImpl<FmbExchangeCodesMapper, FmbExchangeCodes> implements IFmbExchangeCodesService {

}
