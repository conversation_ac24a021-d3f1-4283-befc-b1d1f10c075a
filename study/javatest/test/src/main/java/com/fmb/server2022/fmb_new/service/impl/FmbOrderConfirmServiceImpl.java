package main.java.com.fmb.server2022.fmb_new.service.impl;

import com.fmb.server2022.fmb_new.entity.FmbOrderConfirm;
import com.fmb.server2022.fmb_new.mapper.FmbOrderConfirmMapper;
import com.fmb.server2022.fmb_new.service.IFmbOrderConfirmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店预订表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
public class FmbOrderConfirmServiceImpl extends ServiceImpl<FmbOrderConfirmMapper, FmbOrderConfirm> implements IFmbOrderConfirmService {

}
