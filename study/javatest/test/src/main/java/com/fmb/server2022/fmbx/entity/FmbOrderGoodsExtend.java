package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单order_goods扩展表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_goods_extend")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderGoodsExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * order_goods表rec_id
     */
    private Integer recId;

    /**
     * 货物售价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }

    @Override
    public String toString() {
        return "FmbOrderGoodsExtend{" +
            "id=" + id +
            ", recId=" + recId +
            ", goodsPrice=" + goodsPrice +
            ", settlePrice=" + settlePrice +
        "}";
    }
}
