package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 商品分类
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_goods_category")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbGoodsCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @TableId(value = "cate_id", type = IdType.AUTO)
    private Integer cateId;

    /**
     * 父ID
     */
    private Integer parentid;

    /**
     * 所有父ID
     */
    private String arrparentid;

    /**
     * 子ID数目
     */
    private Boolean child;

    /**
     * 所有子ID
     */
    private String arrchildid;

    /**
     * 分类名称
     */
    private String cateName;

    /**
     * 列表顺序,越大越靠前
     */
    private Integer listorder;

    private Boolean isShield;

    public Integer getCateId() {
        return cateId;
    }

    public void setCateId(Integer cateId) {
        this.cateId = cateId;
    }
    public Integer getParentid() {
        return parentid;
    }

    public void setParentid(Integer parentid) {
        this.parentid = parentid;
    }
    public String getArrparentid() {
        return arrparentid;
    }

    public void setArrparentid(String arrparentid) {
        this.arrparentid = arrparentid;
    }
    public Boolean getChild() {
        return child;
    }

    public void setChild(Boolean child) {
        this.child = child;
    }
    public String getArrchildid() {
        return arrchildid;
    }

    public void setArrchildid(String arrchildid) {
        this.arrchildid = arrchildid;
    }
    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }
    public Integer getListorder() {
        return listorder;
    }

    public void setListorder(Integer listorder) {
        this.listorder = listorder;
    }
    public Boolean getIsShield() {
        return isShield;
    }

    public void setIsShield(Boolean isShield) {
        this.isShield = isShield;
    }

    @Override
    public String toString() {
        return "FmbGoodsCategory{" +
            "cateId=" + cateId +
            ", parentid=" + parentid +
            ", arrparentid=" + arrparentid +
            ", child=" + child +
            ", arrchildid=" + arrchildid +
            ", cateName=" + cateName +
            ", listorder=" + listorder +
            ", isShield=" + isShield +
        "}";
    }
}
