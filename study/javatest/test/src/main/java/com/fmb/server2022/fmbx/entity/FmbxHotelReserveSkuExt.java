package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 房劵扩展表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_hotel_reserve_sku_ext")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxHotelReserveSkuExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.ASSIGN_ID)
    private Integer skuId;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 购买须知
     */
    private String buyNote;

    /**
     * 支付成功短信
     */
    private String smsPaySuccess;

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public String getBuyNote() {
        return buyNote;
    }

    public void setBuyNote(String buyNote) {
        this.buyNote = buyNote;
    }
    public String getSmsPaySuccess() {
        return smsPaySuccess;
    }

    public void setSmsPaySuccess(String smsPaySuccess) {
        this.smsPaySuccess = smsPaySuccess;
    }

    @Override
    public String toString() {
        return "FmbxHotelReserveSkuExt{" +
            "skuId=" + skuId +
            ", versionNum=" + versionNum +
            ", buyNote=" + buyNote +
            ", smsPaySuccess=" + smsPaySuccess +
        "}";
    }
}
