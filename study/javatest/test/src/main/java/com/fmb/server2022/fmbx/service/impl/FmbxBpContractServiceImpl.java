package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpContract;
import com.fmb.server2022.fmbx.mapper.FmbxBpContractMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpContractService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商合同表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-10-16
 */
@Service
public class FmbxBpContractServiceImpl extends ServiceImpl<FmbxBpContractMapper, FmbxBpContract> implements IFmbxBpContractService {

}
