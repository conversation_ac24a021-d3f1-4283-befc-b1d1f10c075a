package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuiteStockOperate;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteStockOperateMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockOperateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 套餐修改日志 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-12-12
 */
@Service
public class FmbxSuiteStockOperateServiceImpl extends ServiceImpl<FmbxSuiteStockOperateMapper, FmbxSuiteStockOperate> implements IFmbxSuiteStockOperateService {

}
