package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.mapper.FmbxActivityMapper;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * fmbx活动主表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-17
 */
@Service
public class FmbxActivityServiceImpl extends ServiceImpl<FmbxActivityMapper, FmbxActivity> implements IFmbxActivityService {

}
