package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 套餐表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-12-02
 */
@Service
public class FmbxSuiteServiceImpl extends ServiceImpl<FmbxSuiteMapper, FmbxSuite> implements IFmbxSuiteService {

}
