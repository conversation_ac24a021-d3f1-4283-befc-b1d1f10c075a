package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbHotelReserveOrderRecord;
import com.fmb.server2022.fmbx.mapper.FmbHotelReserveOrderRecordMapper;
import com.fmb.server2022.fmbx.service.IFmbHotelReserveOrderRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店预约记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-17
 */
@Service
public class FmbHotelReserveOrderRecordServiceImpl extends ServiceImpl<FmbHotelReserveOrderRecordMapper, FmbHotelReserveOrderRecord> implements IFmbHotelReserveOrderRecordService {

}
