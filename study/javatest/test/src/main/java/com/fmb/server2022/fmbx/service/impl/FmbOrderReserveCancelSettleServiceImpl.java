package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderReserveCancelSettle;
import com.fmb.server2022.fmbx.mapper.FmbOrderReserveCancelSettleMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderReserveCancelSettleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 已结算预约取消信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class FmbOrderReserveCancelSettleServiceImpl extends ServiceImpl<FmbOrderReserveCancelSettleMapper, FmbOrderReserveCancelSettle> implements IFmbOrderReserveCancelSettleService {

}
