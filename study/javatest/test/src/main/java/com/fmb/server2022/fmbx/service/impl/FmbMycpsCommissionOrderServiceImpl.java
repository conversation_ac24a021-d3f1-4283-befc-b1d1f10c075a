package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbMycpsCommissionOrder;
import com.fmb.server2022.fmbx.mapper.FmbMycpsCommissionOrderMapper;
import com.fmb.server2022.fmbx.service.IFmbMycpsCommissionOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单佣金信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-28
 */
@Service
public class FmbMycpsCommissionOrderServiceImpl extends ServiceImpl<FmbMycpsCommissionOrderMapper, FmbMycpsCommissionOrder> implements IFmbMycpsCommissionOrderService {

}
