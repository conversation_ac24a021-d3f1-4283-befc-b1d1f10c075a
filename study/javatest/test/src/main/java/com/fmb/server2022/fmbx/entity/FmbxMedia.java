package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.format.annotation.DateTimeFormat;


import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 文件上传表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_media")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxMedia implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "media_id", type = IdType.AUTO)
    private Integer mediaId;

    /**
     * 存储相对路径
     */
    private String savePath;

    /**
     * 原始文件名称
     */
    private String sourceFileName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 文件扩展名
     */
    private String fileExtType;

    /**
     * 网页地址
     */
    private String url;

    private String videoSnapUrl;

    /**
     * 后台用户类型:1-父母邦后台,2-商家后台
     */
    private Integer adminType;

    /**
     * 文件大小
     */
    private Integer length;

    /**
     * 用户uid
     */
    private Integer adminUid;

    /**
     * urlmd5值
     */
    private String md5;


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getMediaId() {
        return mediaId;
    }

    public void setMediaId(Integer mediaId) {
        this.mediaId = mediaId;
    }
    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }
    public String getSourceFileName() {
        return sourceFileName;
    }

    public void setSourceFileName(String sourceFileName) {
        this.sourceFileName = sourceFileName;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getFileExtType() {
        return fileExtType;
    }

    public void setFileExtType(String fileExtType) {
        this.fileExtType = fileExtType;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }

    public String getVideoSnapUrl() {
        return videoSnapUrl;
    }

    public void setVideoSnapUrl(String videoSnapUrl) {
        this.videoSnapUrl = videoSnapUrl;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    @Override
    public String toString() {
        return "FmbxMedia{" +
                "mediaId=" + mediaId +
                ", savePath='" + savePath + '\'' +
                ", sourceFileName='" + sourceFileName + '\'' +
                ", ctime=" + ctime +
                ", fileExtType='" + fileExtType + '\'' +
                ", url='" + url + '\'' +
                ", videoSnapUrl='" + videoSnapUrl + '\'' +
                ", adminType=" + adminType +
                ", length=" + length +
                ", adminUid=" + adminUid +
                ", md5=" + md5 +
                '}';
    }
}
