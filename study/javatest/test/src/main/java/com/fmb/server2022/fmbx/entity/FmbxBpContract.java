package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商合同表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_contract")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpContract implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bpc_id", type = IdType.AUTO)
    private Integer bpcId;

    /**
     * 供应商id
     */
    private Integer bpId;

    /**
     * 审批中合同状态:0-发起申请,1-直属经理审批,2-法务审批,3-上海财务审批,4-CEO审批,5-合同归档提交,6-合同确认无误,7-拒绝
     */
    private Integer bpcRunuingStatus;

    /**
     * 合同有效期状态:1-有效期内,2-已经过期
     */
    private Integer bpcAllStatus;

    /**
     * 订单审核完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime bpDingCheckTime;

    /**
     * 钉钉单号
     */
    private String bpDingProcessid;

    /**
     * 订单拒绝原因
     */
    private String bpDingRejectInfo;

    /**
     * 合同编号
     */
    private String bpcNumber;

    /**
     * 分站:1-北京,2-上海
     */
    private Integer cityId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线,20-运营
     */
    private Integer businessType;

    /**
     * 合同形式:1-标准合同,2-非标准合同
     */
    private Integer bpTemplateType;

    /**
     * 合同类型:1-新建,2-续签,3-补充协议
     */
    private Integer bpCreateType;

    /**
     * 合同名称
     */
    private String bpcName;

    /**
     * 补充协议所属主合同号
     */
    private String bpReplenishNumber;

    /**
     * 合同甲方
     */
    private String bpUserA;

    /**
     * 合同乙方
     */
    private String bpUserB;

    /**
     * 合同丙方
     */
    private String bpUserC;

    /**
     * 售卖商品
     */
    private String bpSaleProductInfo;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate bpStartDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate bpEndDate;

    /**
     * 合同期限说明
     */
    private String bpcDateNote;

    /**
     * 合同总金额
     */
    private String bpcAllMoney;

    /**
     * 具体售卖产品
     */
    private String productDetail;

    /**
     * 预付款保证金说明
     */
    private String prepayInfo;

    /**
     * 历史预付款
     */
    private Integer historyPrePayInfo;

    /**
     * 预付款保证金
     */
    private Integer prePay;

    /**
     * 预付款保证金说明
     */
    private String prePayNote;

    /**
     * 毛利率
     */
    private BigDecimal grossRate;

    /**
     * 是否提供发票
     */
    private String invoiceInfo;

    /**
     * 项目负责人
     */
    private String productLeader;

    /**
     * 合同简介
     */
    private String bpDesc;

    /**
     * 申请用印
     */
    private String reqSeal;

    /**
     * 合同类型:1-父母邦先用印,2-商家先用印
     */
    private Integer bpSealType;

    /**
     * 回寄相关信息备注
     */
    private String bpReciverNote;

    /**
     * 快递单号
     */
    private String bpReciverExpressNo;

    /**
     * 快递公司
     */
    private String bpReciverExpressCompany;

    /**
     * 合同收件人
     */
    private String bpReciverName;

    /**
     * 用印情况:1-双方已盖章已归档,2-我方先盖章未归档,3-不予受理
     */
    private String bpSealUseInfo;

    /**
     * 不受理说明
     */
    private String bpSealNoNote;

    /**
     * 合同备注
     */
    private String bpNote;

    /**
     * 业务备注
     */
    private String bpBusinessNote;

    /**
     * 钉钉操作流程
     */
    private String dingdingOpDetail;

    /**
     * 拒绝意见列表
     */
    private String rejectTableInfo;

    /**
     * 钉钉userid
     */
    private String createrDingUserid;

    /**
     * 主合同编号
     */
    private String bpcNumberOfMain ;

    /**
     * 定期检查即将结束标记:1-检查过了,0-没检查
     */
    private Integer willendCheckStatus;

    public Integer getBpcId() {
        return bpcId;
    }

    public void setBpcId(Integer bpcId) {
        this.bpcId = bpcId;
    }
    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getBpcRunuingStatus() {
        return bpcRunuingStatus;
    }

    public void setBpcRunuingStatus(Integer bpcRunuingStatus) {
        this.bpcRunuingStatus = bpcRunuingStatus;
    }
    public Integer getBpcAllStatus() {
        return bpcAllStatus;
    }

    public void setBpcAllStatus(Integer bpcAllStatus) {
        this.bpcAllStatus = bpcAllStatus;
    }
    public LocalDateTime getBpDingCheckTime() {
        return bpDingCheckTime;
    }

    public void setBpDingCheckTime(LocalDateTime bpDingCheckTime) {
        this.bpDingCheckTime = bpDingCheckTime;
    }
    public String getBpDingProcessid() {
        return bpDingProcessid;
    }

    public void setBpDingProcessid(String bpDingProcessid) {
        this.bpDingProcessid = bpDingProcessid;
    }
    public String getBpDingRejectInfo() {
        return bpDingRejectInfo;
    }

    public void setBpDingRejectInfo(String bpDingRejectInfo) {
        this.bpDingRejectInfo = bpDingRejectInfo;
    }
    public String getBpcNumber() {
        return bpcNumber;
    }

    public void setBpcNumber(String bpcNumber) {
        this.bpcNumber = bpcNumber;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
    public Integer getBpTemplateType() {
        return bpTemplateType;
    }

    public void setBpTemplateType(Integer bpTemplateType) {
        this.bpTemplateType = bpTemplateType;
    }
    public Integer getBpCreateType() {
        return bpCreateType;
    }

    public void setBpCreateType(Integer bpCreateType) {
        this.bpCreateType = bpCreateType;
    }
    public String getBpcName() {
        return bpcName;
    }

    public void setBpcName(String bpcName) {
        this.bpcName = bpcName;
    }
    public String getBpReplenishNumber() {
        return bpReplenishNumber;
    }

    public void setBpReplenishNumber(String bpReplenishNumber) {
        this.bpReplenishNumber = bpReplenishNumber;
    }
    public String getBpUserA() {
        return bpUserA;
    }

    public void setBpUserA(String bpUserA) {
        this.bpUserA = bpUserA;
    }
    public String getBpUserB() {
        return bpUserB;
    }

    public void setBpUserB(String bpUserB) {
        this.bpUserB = bpUserB;
    }
    public String getBpUserC() {
        return bpUserC;
    }

    public void setBpUserC(String bpUserC) {
        this.bpUserC = bpUserC;
    }
    public String getBpSaleProductInfo() {
        return bpSaleProductInfo;
    }

    public void setBpSaleProductInfo(String bpSaleProductInfo) {
        this.bpSaleProductInfo = bpSaleProductInfo;
    }
    public LocalDate getBpStartDate() {
        return bpStartDate;
    }

    public void setBpStartDate(LocalDate bpStartDate) {
        this.bpStartDate = bpStartDate;
    }
    public LocalDate getBpEndDate() {
        return bpEndDate;
    }

    public void setBpEndDate(LocalDate bpEndDate) {
        this.bpEndDate = bpEndDate;
    }
    public String getBpcDateNote() {
        return bpcDateNote;
    }

    public void setBpcDateNote(String bpcDateNote) {
        this.bpcDateNote = bpcDateNote;
    }
    public String getBpcAllMoney() {
        return bpcAllMoney;
    }

    public void setBpcAllMoney(String bpcAllMoney) {
        this.bpcAllMoney = bpcAllMoney;
    }
    public String getProductDetail() {
        return productDetail;
    }

    public void setProductDetail(String productDetail) {
        this.productDetail = productDetail;
    }
    public String getPrepayInfo() {
        return prepayInfo;
    }

    public void setPrepayInfo(String prepayInfo) {
        this.prepayInfo = prepayInfo;
    }
    public Integer getHistoryPrePayInfo() {
        return historyPrePayInfo;
    }

    public void setHistoryPrePayInfo(Integer historyPrePayInfo) {
        this.historyPrePayInfo = historyPrePayInfo;
    }
    public Integer getPrePay() {
        return prePay;
    }

    public void setPrePay(Integer prePay) {
        this.prePay = prePay;
    }
    public String getPrePayNote() {
        return prePayNote;
    }

    public void setPrePayNote(String prePayNote) {
        this.prePayNote = prePayNote;
    }
    public BigDecimal getGrossRate() {
        return grossRate;
    }

    public void setGrossRate(BigDecimal grossRate) {
        this.grossRate = grossRate;
    }
    public String getInvoiceInfo() {
        return invoiceInfo;
    }

    public void setInvoiceInfo(String invoiceInfo) {
        this.invoiceInfo = invoiceInfo;
    }
    public String getProductLeader() {
        return productLeader;
    }

    public void setProductLeader(String productLeader) {
        this.productLeader = productLeader;
    }
    public String getBpDesc() {
        return bpDesc;
    }

    public void setBpDesc(String bpDesc) {
        this.bpDesc = bpDesc;
    }
    public String getReqSeal() {
        return reqSeal;
    }

    public void setReqSeal(String reqSeal) {
        this.reqSeal = reqSeal;
    }
    public Integer getBpSealType() {
        return bpSealType;
    }

    public void setBpSealType(Integer bpSealType) {
        this.bpSealType = bpSealType;
    }
    public String getBpReciverNote() {
        return bpReciverNote;
    }

    public void setBpReciverNote(String bpReciverNote) {
        this.bpReciverNote = bpReciverNote;
    }
    public String getBpReciverExpressNo() {
        return bpReciverExpressNo;
    }

    public void setBpReciverExpressNo(String bpReciverExpressNo) {
        this.bpReciverExpressNo = bpReciverExpressNo;
    }
    public String getBpReciverExpressCompany() {
        return bpReciverExpressCompany;
    }

    public void setBpReciverExpressCompany(String bpReciverExpressCompany) {
        this.bpReciverExpressCompany = bpReciverExpressCompany;
    }
    public String getBpReciverName() {
        return bpReciverName;
    }

    public void setBpReciverName(String bpReciverName) {
        this.bpReciverName = bpReciverName;
    }
    public String getBpSealUseInfo() {
        return bpSealUseInfo;
    }

    public void setBpSealUseInfo(String bpSealUseInfo) {
        this.bpSealUseInfo = bpSealUseInfo;
    }
    public String getBpSealNoNote() {
        return bpSealNoNote;
    }

    public void setBpSealNoNote(String bpSealNoNote) {
        this.bpSealNoNote = bpSealNoNote;
    }
    public String getBpNote() {
        return bpNote;
    }

    public void setBpNote(String bpNote) {
        this.bpNote = bpNote;
    }
    public String getBpBusinessNote() {
        return bpBusinessNote;
    }

    public void setBpBusinessNote(String bpBusinessNote) {
        this.bpBusinessNote = bpBusinessNote;
    }
    public String getDingdingOpDetail() {
        return dingdingOpDetail;
    }

    public void setDingdingOpDetail(String dingdingOpDetail) {
        this.dingdingOpDetail = dingdingOpDetail;
    }
    public String getRejectTableInfo() {
        return rejectTableInfo;
    }

    public void setRejectTableInfo(String rejectTableInfo) {
        this.rejectTableInfo = rejectTableInfo;
    }
    public String getCreaterDingUserid() {
        return createrDingUserid;
    }

    public void setCreaterDingUserid(String createrDingUserid) {
        this.createrDingUserid = createrDingUserid;
    }
    public Integer getWillendCheckStatus() {
        return willendCheckStatus;
    }

    public void setWillendCheckStatus(Integer willendCheckStatus) {
        this.willendCheckStatus = willendCheckStatus;
    }


    public String getBpcNumberOfMain() {
        return bpcNumberOfMain;
    }

    public void setBpcNumberOfMain(String bpcNumberOfMain) {
        this.bpcNumberOfMain = bpcNumberOfMain;
    }

    @Override
    public String toString() {
        return "FmbxBpContract{" +
            "bpcId=" + bpcId +
            ", bpId=" + bpId +
            ", bpcRunuingStatus=" + bpcRunuingStatus +
            ", bpcAllStatus=" + bpcAllStatus +
            ", bpDingCheckTime=" + bpDingCheckTime +
            ", bpDingProcessid=" + bpDingProcessid +
            ", bpDingRejectInfo=" + bpDingRejectInfo +
            ", bpcNumber=" + bpcNumber +
            ", cityId=" + cityId +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", businessType=" + businessType +
            ", bpTemplateType=" + bpTemplateType +
            ", bpCreateType=" + bpCreateType +
            ", bpcName=" + bpcName +
            ", bpReplenishNumber=" + bpReplenishNumber +
            ", bpUserA=" + bpUserA +
            ", bpUserB=" + bpUserB +
            ", bpUserC=" + bpUserC +
            ", bpSaleProductInfo=" + bpSaleProductInfo +
            ", bpStartDate=" + bpStartDate +
            ", bpEndDate=" + bpEndDate +
            ", bpcDateNote=" + bpcDateNote +
            ", bpcAllMoney=" + bpcAllMoney +
            ", productDetail=" + productDetail +
            ", prepayInfo=" + prepayInfo +
            ", historyPrePayInfo=" + historyPrePayInfo +
            ", prePay=" + prePay +
            ", prePayNote=" + prePayNote +
            ", grossRate=" + grossRate +
            ", invoiceInfo=" + invoiceInfo +
            ", productLeader=" + productLeader +
            ", bpDesc=" + bpDesc +
            ", reqSeal=" + reqSeal +
            ", bpSealType=" + bpSealType +
            ", bpReciverNote=" + bpReciverNote +
            ", bpReciverExpressNo=" + bpReciverExpressNo +
            ", bpReciverExpressCompany=" + bpReciverExpressCompany +
            ", bpReciverName=" + bpReciverName +
            ", bpSealUseInfo=" + bpSealUseInfo +
            ", bpSealNoNote=" + bpSealNoNote +
            ", bpNote=" + bpNote +
            ", bpBusinessNote=" + bpBusinessNote +
            ", dingdingOpDetail=" + dingdingOpDetail +
            ", rejectTableInfo=" + rejectTableInfo +
            ", createrDingUserid=" + createrDingUserid +
            ", willendCheckStatus=" + willendCheckStatus +
            ", bpcNumberOfMain=" + bpcNumberOfMain +
        "}";
    }
}
