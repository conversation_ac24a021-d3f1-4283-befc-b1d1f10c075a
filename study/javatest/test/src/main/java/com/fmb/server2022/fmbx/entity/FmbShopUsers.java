package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 商家帐号信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_shop_users")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbShopUsers implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "shop_user_id", type = IdType.AUTO)
    private Integer shopUserId;

    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 0 主账号 1 子账号
     */
    private Integer shopType;

    /**
     * 商家登录名称
     */
    private String username;

    /**
     * 商家登录密码
     */
    private String password;

    /**
     * 关联商家ID
     */
    private Integer shopId;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 商家logo图片
     */
    private String logo;

    /**
     * 市场负责人
     */
    private String head;

    /**
     * 市场对接人手机
     */
    private String headMobile;

    /**
     * 市场对接人邮件
     */
    private String headEmail;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 公司职位
     */
    private String position;

    /**
     * 合作方对接人邮箱
     */
    private String partnerEmail;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 固定电话
     */
    private String tel;

    /**
     * 传真号
     */
    private String fax;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 商家政策
     */
    private String policy;

    /**
     * 商务联系人姓名
     */
    private String financeContact;

    /**
     * 财务联系人电话
     */
    private String financeTel;

    /**
     * 财务联系人邮箱
     */
    private String financeEmail;

    /**
     * 财务联系人传真
     */
    private String financeFax;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 商家开户行
     */
    private String shopBank;

    /**
     * 商家账户名称
     */
    private String shopAccount;

    /**
     * 商家账户号
     */
    private String shopAccountSn;

    /**
     * 结算周期
     */
    private Integer isCycle;

    /**
     * 结算周期
     */
    private Integer cycleType;

    /**
     * 结算日期
     */
    private Integer settleCycle;

    /**
     * 结算提醒
     */
    private Integer settleAlert;

    /**
     * 最后登陆IP
     */
    private String lastLoginIp;

    /**
     * 最后登陆时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastLoginTime;

    /**
     * 账号状态1可用，2禁用
     */
    private Integer status;

    /**
     * 商户后台是否显示库存模块
     */
    private Integer isStockShow;

    /**
     * 0:不限 1:演出 2:收费活动 3:酒店 4:报名活动
     */
    private Integer type;

    /**
     * 预订部联系人
     */
    private String bookContact;

    /**
     * 预订部工作时间
     */
    private String bookTime;

    /**
     * 订房邮箱抄送
     */
    private String bookMailCc;

    /**
     * 订房单发送方式
     */
    private String bookContactMode;

    /**
     * 预定部其他联系方式
     */
    private String bookContactOther;

    /**
     * 商家后台展示用户手机号 1:显示 0：不显示
     */
    private Integer isShowMobile;

    /**
     * 通知销售保房时间
     */
    private String informSales;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 对方对父母邦的称呼
     */
    private String signCompany;

    /**
     * 酒店订房单是否显示印章
     */
    private Integer isShowSeal;

    /**
     * 营业执照
     */
    private String businessLicenseList;

    /**
     * 税务登记
     */
    private String taxList;

    /**
     * 组织机构代码证
     */
    private String organizationList;

    /**
     * 合同文件
     */
    private String contractFileList;

    /**
     * 商家价格确认函
     */
    private String shopPriceConfirmationList;

    /**
     * 其他附件
     */
    private String otherList;

    /**
     * 实体票是否展示:0-不展示，1-展示
     */
    private Integer isEntityTicket;

    /**
     * 商家后台隐藏无货-拒绝 1:隐藏 0：不隐藏
     */
    private Integer isShowRejectNoGoods;

    /**
     * 商家后台展示用户姓名 1:不展示 0：展示
     */
    private Integer isShowCustomerName;

    /**
     * 取消预约 0否 1是 默认0
     */
    private Integer cacelReserve;

    /**
     * 展示销售数据 0否 1是 默认0
     */
    private Integer showOrderStat;

    /**
     * 是否允许商家上架、下架票种:0-否;1-是
     */
    private Integer isUpTicketsShow;

    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }
    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }
    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }
    public String getHeadMobile() {
        return headMobile;
    }

    public void setHeadMobile(String headMobile) {
        this.headMobile = headMobile;
    }
    public String getHeadEmail() {
        return headEmail;
    }

    public void setHeadEmail(String headEmail) {
        this.headEmail = headEmail;
    }
    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }
    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
    public String getPartnerEmail() {
        return partnerEmail;
    }

    public void setPartnerEmail(String partnerEmail) {
        this.partnerEmail = partnerEmail;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }
    public String getFinanceContact() {
        return financeContact;
    }

    public void setFinanceContact(String financeContact) {
        this.financeContact = financeContact;
    }
    public String getFinanceTel() {
        return financeTel;
    }

    public void setFinanceTel(String financeTel) {
        this.financeTel = financeTel;
    }
    public String getFinanceEmail() {
        return financeEmail;
    }

    public void setFinanceEmail(String financeEmail) {
        this.financeEmail = financeEmail;
    }
    public String getFinanceFax() {
        return financeFax;
    }

    public void setFinanceFax(String financeFax) {
        this.financeFax = financeFax;
    }
    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }
    public String getShopBank() {
        return shopBank;
    }

    public void setShopBank(String shopBank) {
        this.shopBank = shopBank;
    }
    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }
    public String getShopAccountSn() {
        return shopAccountSn;
    }

    public void setShopAccountSn(String shopAccountSn) {
        this.shopAccountSn = shopAccountSn;
    }
    public Integer getIsCycle() {
        return isCycle;
    }

    public void setIsCycle(Integer isCycle) {
        this.isCycle = isCycle;
    }
    public Integer getCycleType() {
        return cycleType;
    }

    public void setCycleType(Integer cycleType) {
        this.cycleType = cycleType;
    }
    public Integer getSettleCycle() {
        return settleCycle;
    }

    public void setSettleCycle(Integer settleCycle) {
        this.settleCycle = settleCycle;
    }
    public Integer getSettleAlert() {
        return settleAlert;
    }

    public void setSettleAlert(Integer settleAlert) {
        this.settleAlert = settleAlert;
    }
    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }
    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getIsStockShow() {
        return isStockShow;
    }

    public void setIsStockShow(Integer isStockShow) {
        this.isStockShow = isStockShow;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getBookContact() {
        return bookContact;
    }

    public void setBookContact(String bookContact) {
        this.bookContact = bookContact;
    }
    public String getBookTime() {
        return bookTime;
    }

    public void setBookTime(String bookTime) {
        this.bookTime = bookTime;
    }
    public String getBookMailCc() {
        return bookMailCc;
    }

    public void setBookMailCc(String bookMailCc) {
        this.bookMailCc = bookMailCc;
    }
    public String getBookContactMode() {
        return bookContactMode;
    }

    public void setBookContactMode(String bookContactMode) {
        this.bookContactMode = bookContactMode;
    }
    public String getBookContactOther() {
        return bookContactOther;
    }

    public void setBookContactOther(String bookContactOther) {
        this.bookContactOther = bookContactOther;
    }
    public Integer getIsShowMobile() {
        return isShowMobile;
    }

    public void setIsShowMobile(Integer isShowMobile) {
        this.isShowMobile = isShowMobile;
    }
    public String getInformSales() {
        return informSales;
    }

    public void setInformSales(String informSales) {
        this.informSales = informSales;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getSignCompany() {
        return signCompany;
    }

    public void setSignCompany(String signCompany) {
        this.signCompany = signCompany;
    }
    public Integer getIsShowSeal() {
        return isShowSeal;
    }

    public void setIsShowSeal(Integer isShowSeal) {
        this.isShowSeal = isShowSeal;
    }
    public String getBusinessLicenseList() {
        return businessLicenseList;
    }

    public void setBusinessLicenseList(String businessLicenseList) {
        this.businessLicenseList = businessLicenseList;
    }
    public String getTaxList() {
        return taxList;
    }

    public void setTaxList(String taxList) {
        this.taxList = taxList;
    }
    public String getOrganizationList() {
        return organizationList;
    }

    public void setOrganizationList(String organizationList) {
        this.organizationList = organizationList;
    }
    public String getContractFileList() {
        return contractFileList;
    }

    public void setContractFileList(String contractFileList) {
        this.contractFileList = contractFileList;
    }
    public String getShopPriceConfirmationList() {
        return shopPriceConfirmationList;
    }

    public void setShopPriceConfirmationList(String shopPriceConfirmationList) {
        this.shopPriceConfirmationList = shopPriceConfirmationList;
    }
    public String getOtherList() {
        return otherList;
    }

    public void setOtherList(String otherList) {
        this.otherList = otherList;
    }
    public Integer getIsEntityTicket() {
        return isEntityTicket;
    }

    public void setIsEntityTicket(Integer isEntityTicket) {
        this.isEntityTicket = isEntityTicket;
    }
    public Integer getIsShowRejectNoGoods() {
        return isShowRejectNoGoods;
    }

    public void setIsShowRejectNoGoods(Integer isShowRejectNoGoods) {
        this.isShowRejectNoGoods = isShowRejectNoGoods;
    }
    public Integer getIsShowCustomerName() {
        return isShowCustomerName;
    }

    public void setIsShowCustomerName(Integer isShowCustomerName) {
        this.isShowCustomerName = isShowCustomerName;
    }
    public Integer getCacelReserve() {
        return cacelReserve;
    }

    public void setCacelReserve(Integer cacelReserve) {
        this.cacelReserve = cacelReserve;
    }
    public Integer getShowOrderStat() {
        return showOrderStat;
    }

    public void setShowOrderStat(Integer showOrderStat) {
        this.showOrderStat = showOrderStat;
    }
    public Integer getIsUpTicketsShow() {
        return isUpTicketsShow;
    }

    public void setIsUpTicketsShow(Integer isUpTicketsShow) {
        this.isUpTicketsShow = isUpTicketsShow;
    }

    @Override
    public String toString() {
        return "FmbShopUsers{" +
            "shopUserId=" + shopUserId +
            ", pid=" + pid +
            ", shopType=" + shopType +
            ", username=" + username +
            ", password=" + password +
            ", shopId=" + shopId +
            ", cityId=" + cityId +
            ", logo=" + logo +
            ", head=" + head +
            ", headMobile=" + headMobile +
            ", headEmail=" + headEmail +
            ", realName=" + realName +
            ", position=" + position +
            ", partnerEmail=" + partnerEmail +
            ", mobile=" + mobile +
            ", tel=" + tel +
            ", fax=" + fax +
            ", email=" + email +
            ", address=" + address +
            ", policy=" + policy +
            ", financeContact=" + financeContact +
            ", financeTel=" + financeTel +
            ", financeEmail=" + financeEmail +
            ", financeFax=" + financeFax +
            ", invoiceTitle=" + invoiceTitle +
            ", shopBank=" + shopBank +
            ", shopAccount=" + shopAccount +
            ", shopAccountSn=" + shopAccountSn +
            ", isCycle=" + isCycle +
            ", cycleType=" + cycleType +
            ", settleCycle=" + settleCycle +
            ", settleAlert=" + settleAlert +
            ", lastLoginIp=" + lastLoginIp +
            ", lastLoginTime=" + lastLoginTime +
            ", status=" + status +
            ", isStockShow=" + isStockShow +
            ", type=" + type +
            ", bookContact=" + bookContact +
            ", bookTime=" + bookTime +
            ", bookMailCc=" + bookMailCc +
            ", bookContactMode=" + bookContactMode +
            ", bookContactOther=" + bookContactOther +
            ", isShowMobile=" + isShowMobile +
            ", informSales=" + informSales +
            ", ctime=" + ctime +
            ", signCompany=" + signCompany +
            ", isShowSeal=" + isShowSeal +
            ", businessLicenseList=" + businessLicenseList +
            ", taxList=" + taxList +
            ", organizationList=" + organizationList +
            ", contractFileList=" + contractFileList +
            ", shopPriceConfirmationList=" + shopPriceConfirmationList +
            ", otherList=" + otherList +
            ", isEntityTicket=" + isEntityTicket +
            ", isShowRejectNoGoods=" + isShowRejectNoGoods +
            ", isShowCustomerName=" + isShowCustomerName +
            ", cacelReserve=" + cacelReserve +
            ", showOrderStat=" + showOrderStat +
            ", isUpTicketsShow=" + isUpTicketsShow +
        "}";
    }
}
