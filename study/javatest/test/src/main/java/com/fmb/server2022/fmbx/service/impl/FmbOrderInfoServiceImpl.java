package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.mapper.FmbOrderInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 用户订单信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbOrderInfoServiceImpl extends ServiceImpl<FmbOrderInfoMapper, FmbOrderInfo> implements IFmbOrderInfoService {

}
