package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpParterHotelInfo;
import com.fmb.server2022.fmbx.mapper.FmbxBpParterHotelInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 供应商酒店预定信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-25
 */
@Service
public class FmbxBpParterHotelInfoServiceImpl extends ServiceImpl<FmbxBpParterHotelInfoMapper, FmbxBpParterHotelInfo> implements IFmbxBpParterHotelInfoService {

}
