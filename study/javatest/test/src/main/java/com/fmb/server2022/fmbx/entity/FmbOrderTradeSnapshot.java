package main.java.com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单交易快照表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_trade_snapshot")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderTradeSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "snapshot_id", type = IdType.AUTO)
    private Integer snapshotId;

    /**
     * 关联订单对应的具体商品表id,即与fmb_order_goods表rec_id关联
     */
    private Integer recId;

    private String orderSn;

    /**
     * 订单用户uid
     */
    private Integer uid;

    /**
     * 快照基本信息
     */
    private String basicInfo;

    /**
     * 快照内容
     */
    private String content;

    /**
     * 手机图文详情
     */
    private String appContent;

    /**
     * 手机活动详情专供APP使用内容
     */
    private String appContentNew;

    /**
     * 活动摘要及规则
     */
    private String summary;

    /**
     * 活动关联的周边景点内容
     */
    private String nearScenerys;

    /**
     * 1-演出，2-景点，3-通用，4-酒店
     */
    private Integer goodsType;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 是否为新老数据 0 老数据 ，1 新数据
     */
    private Integer isOldData;

    public Integer getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(Integer snapshotId) {
        this.snapshotId = snapshotId;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(String basicInfo) {
        this.basicInfo = basicInfo;
    }
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public String getAppContent() {
        return appContent;
    }

    public void setAppContent(String appContent) {
        this.appContent = appContent;
    }
    public String getAppContentNew() {
        return appContentNew;
    }

    public void setAppContentNew(String appContentNew) {
        this.appContentNew = appContentNew;
    }
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
    public String getNearScenerys() {
        return nearScenerys;
    }

    public void setNearScenerys(String nearScenerys) {
        this.nearScenerys = nearScenerys;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getIsOldData() {
        return isOldData;
    }

    public void setIsOldData(Integer isOldData) {
        this.isOldData = isOldData;
    }

    @Override
    public String toString() {
        return "FmbOrderTradeSnapshot{" +
            "snapshotId=" + snapshotId +
            ", recId=" + recId +
            ", orderSn=" + orderSn +
            ", uid=" + uid +
            ", basicInfo=" + basicInfo +
            ", content=" + content +
            ", appContent=" + appContent +
            ", appContentNew=" + appContentNew +
            ", summary=" + summary +
            ", nearScenerys=" + nearScenerys +
            ", goodsType=" + goodsType +
            ", ctime=" + ctime +
            ", isOldData=" + isOldData +
        "}";
    }
}
