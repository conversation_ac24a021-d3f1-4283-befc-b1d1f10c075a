package main.java.com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbMycpsUser;
import com.fmb.server2022.fmbx.mapper.FmbMycpsUserMapper;
import com.fmb.server2022.fmbx.service.IFmbMycpsUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 分销商与用户关联表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-06-28
 */
@Service
public class FmbMycpsUserServiceImpl extends ServiceImpl<FmbMycpsUserMapper, FmbMycpsUser> implements IFmbMycpsUserService {

}
