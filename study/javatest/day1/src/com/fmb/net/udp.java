package com.fmb.net;

import com.fmb.goods.Data;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;

public class udp {
    public static void main(String[] args) throws Exception {
        System.out.println("客户端启动");
        DatagramSocket socket = new DatagramSocket();
        String str = "hello world";
        byte[] bytes = str.getBytes();
        DatagramPacket packet = new DatagramPacket(bytes,bytes.length, InetAddress.getLocalHost(),8080);

        socket.send(packet);
    }
}
