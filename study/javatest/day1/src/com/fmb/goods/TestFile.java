package com.fmb.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class TestFile {
    private String name = "";
    private int age;
    private int id;

    public static void main(String[] args) throws Exception {
        FileInputStream testfile = new FileInputStream("haha.txt");
        byte[] bytes= new byte[1024];
        int i;

        while ((i = testfile.read(bytes))!=-1){
            String str = new String(bytes);
            System.out.println(str);
        }

    }
}
