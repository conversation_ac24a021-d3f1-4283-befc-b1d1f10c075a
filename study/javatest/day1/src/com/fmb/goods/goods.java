package com.fmb.goods;

import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Scanner;



public class goods {
    public static void main(String[] args) throws Exception {

//        File fi = new File("javatest.iml");
        FileInputStream testfile = new FileInputStream("haha.txt");
        byte[] bytes= new byte[3];
        int i;
        while ((i = testfile.read(bytes))!=-1){
//            System.out.print(bytes);
//            System.out.println(i);
            System.out.println(new String(bytes));
        }

        OutputStream os = new FileOutputStream("test.txt");
        os.write('a');
        os.write('b');
        os.write("朝秦暮楚fs".getBytes());
//        ArrayList<String> goods = new ArrayList<>();
//
//        goods.add("aa");
//
//        goods.add("bb");
//
//
//        goods.add("cc");
//        goods.add("dd");
//        for (int i = 0; i < goods.size(); i++) {
//
//        }
//        //        goods.forEach(System.out::println);
//
////        Iterator good = goods.iterator();
//        Iterator<String> good = goods.iterator();
//        while (good.hasNext()) {
//            String s = (String) good.next();
//            System.out.println(s);
//            if(s.equals("bb")){
//                good.remove();
//            }
////            good.remove();
//        }
//        System.out.println(goods);
//
//        goods.iterator().forEachRemaining(System.out::println);
//        goods.remove(0);
//        goods.remove("bb");
//        goods.set(0,"dd");
//
//
//
//        System.out.println(goods);
//
//        for(String s:goods){
//            System.out.println(s);
//        }

//        User a = new Cart("know");
//        a.show();
//        a.name = "cart";

//        User b = new Order();
//        b.show();

//        b.name = "order";
//        System.out.println(a.name);
//        System.out.println(b.name);

//        float a = 1.344f;
//        int b = 1;
//        String cc = "aaa";
//
//        for(int i=0;i<10;i++){
//            System.out.println(i);
//        }
//        switch (cc){
//            case "cc":
//                System.out.println("1");
//                break;
//            case "aaa":
//                System.out.println("2");
//                break;
//            default:
//                System.out.println("default");
//                break;
//        }
//        System.out.println(a);
//        System.out.println("hello world");
//        System.out.println('a');
//        byte a = 127;
//        byte b = 110;
//        int c = getmax(a,b);
//        System.out.println(c);
//        Scanner sc = new Scanner(System.in);
//        int a = sc.nextInt();
//        int b = sc.nextInt();
//        int c = getmax(a,b);
//        System.out.println(c);
//        int a = 5;
//        int d = 2;
//        a /= d;
//        System.out.println(a);
//        System.out.println(a/d);
//        float b = 10.5f;
//        a = a*b;
//        a*=b;
//        int c  = b * a;
//        a*=b;
//        System.out.println(c);
//        System.out.println(a*b);
//        System.out.println((byte)a);
//
//        System.out.println(a+'c');
//
//        List<Integer> list = new ArrayList<>();
//        list.add(1);
//        list.add(2);
//        System.out.println(list.get(0));
//
//        int[] numlist = new int[]{1,2,3};
//        System.out.println(numlist[0]);

//        List<Integer> list = new ArrayList<>();
//        list.add(1);
//        list.add(2);
//        System.out.println(list.get(0));


//        int max = getmax(3,2);
//        System.out.println(max);
    }

//    public static void main(int[] args) {
//
//    }
//
//
//    public boolean addGodds(Good good){
//
//    }

    public static int getmax(int a, int b){
        int c = a>b?a:b;
//        System.out.print(a);
        return c;
//        return a/b;
//        System.out.println("getmax");
//        return a>b?a:b;
    }
}
