package com.fmb.tcp;

import java.io.DataInputStream;
import java.net.ServerSocket;
import java.net.Socket;

public class tcpservice {
    public static void main(String[] args) throws Exception
    {
        System.out.println("服务端启动");
        ServerSocket serverSocket = new ServerSocket(8080);
        Socket accept = serverSocket.accept();
        System.out.println("连接成功");
        System.out.println(accept.getInetAddress().getHostAddress());
        System.out.println(accept.getPort());
        System.out.println(accept.getLocalPort());
        System.out.println(accept.getLocalAddress().getHostAddress());
        //读取数据
        byte[] bytes = new byte[1024];
        DataInputStream read = new DataInputStream(accept.getInputStream());
        String s = read.readUTF();
        int i = read.readInt();
        boolean b = read.readBoolean();
        System.out.println(s);
        System.out.println(i);
        System.out.println(b);
        System.out.println(accept.getInputStream().available());

        read.close();
        accept.close();

    }
}
