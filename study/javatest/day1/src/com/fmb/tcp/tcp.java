package com.fmb.tcp;

import java.io.DataOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;

public class tcp {
    public static void main(String[] args) throws Exception {
        Socket socket = new Socket("127.0.0.1", 8080);
        OutputStream outputStream = socket.getOutputStream();

        DataOutputStream dataOutputStream = new DataOutputStream(outputStream);
        dataOutputStream.writeUTF("hello world");
        dataOutputStream.writeInt(18);
        dataOutputStream.writeBoolean(true);
        dataOutputStream.flush();
        socket.close();
        dataOutputStream.close();
        outputStream.close();
        System.out.println("发送完成");
    }
}
