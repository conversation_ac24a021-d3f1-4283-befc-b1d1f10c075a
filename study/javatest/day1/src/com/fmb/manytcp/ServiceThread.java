package com.fmb.manytcp;

import java.io.DataInputStream;
import java.io.IOException;
import java.net.Socket;

public class ServiceThread  extends Thread{
    Socket accept;
    public ServiceThread(Socket accept) {
        this.accept = accept;
    }

    public void run()
    {

        try {
            byte[] bytes = new byte[1024];
            DataInputStream read = new DataInputStream(accept.getInputStream());

            while (true) {
                String s = read.readUTF();
                System.out.println(s);
                System.out.println(accept.getInputStream().available());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
