package com.fmb.manytcp;

import java.io.IOException;
import java.io.PrintStream;
import java.net.Socket;

public class ThreadRunable implements Runnable {
    Socket accept;
    public ThreadRunable(Socket accept) {
        this.accept = accept;
    }
    @Override
    public void run() {
        try {
            // 首先读取HTTP请求（避免连接挂起）
            byte[] buffer = new byte[1024];
            accept.getInputStream().read(buffer);

            PrintStream htmlshow = new PrintStream(accept.getOutputStream());

            // 构建HTML内容
            String htmlContent = "<html>\r\n<body>\r\n<h1>Hello World</h1>\r\n</body>\r\n</html>\r\n";

            // 发送HTTP响应
            htmlshow.println("HTTP/1.1 200 OK");
            htmlshow.println("Content-Type: text/html; charset=utf-8");
            htmlshow.println("Content-Length: " + htmlContent.getBytes("utf-8").length);
            htmlshow.println("Connection: close");
            htmlshow.println(); // 空行分隔HTTP头部和正文
            htmlshow.print(htmlContent); // 使用print而不是println避免额外换行
            htmlshow.flush(); // 刷新输出流确保数据发送

            // 关闭连接
            htmlshow.close();
            accept.close();
        } catch (IOException e) {
            try {
                accept.close();
            } catch (IOException ex) {
                // 忽略关闭异常
            }
            System.err.println("处理请求时发生错误: " + e.getMessage());
        }
    }
}
