package com.fmb.manytcp;

import java.io.IOException;
import java.io.PrintStream;
import java.net.Socket;

public class ThreadRunable implements Runnable {
    Socket accept;
    public ThreadRunable(Socket accept) {
        this.accept = accept;
    }
    @Override
    public void run() {
//        byte[] bytes = new byte[1024];
        try {
            PrintStream htmlshow = new PrintStream(accept.getOutputStream());
            // 发送html
            htmlshow.println("HTTP/1.1 200 OK");
            htmlshow.println("Content-Type:text/html;charset=utf-8");
            htmlshow.println("Content-Length: 59"); // 添加Content-Length头部
            htmlshow.println(); // 空行分隔HTTP头部和正文
            htmlshow.println("<html>");
            htmlshow.println("<body>");
            htmlshow.println("<h1>Hello World</h1>");
            htmlshow.println("</body>");
            htmlshow.println("</html>");
            htmlshow.flush(); // 刷新输出流确保数据发送
            htmlshow.close();
            accept.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // TODO: 实现线程执行的逻辑
    }
}
