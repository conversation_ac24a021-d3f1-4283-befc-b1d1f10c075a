package com.fmb.manytcp;

import java.io.DataInputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.*;

public class tcpservice {
    public static void main(String[] args) throws Exception
    {
//        System.out.println("服务端启动");
//        ServerSocket serverSocket = new ServerSocket(9999);
//        while (true) {
//            Socket accept = serverSocket.accept();
//            new ServiceThread(accept).start();
//        }


        System.out.println("服务端启动");
        ServerSocket serverSocket = new ServerSocket(9999);
        ExecutorService executorService = new ThreadPoolExecutor(
                10, 
                100, 
                1000, 
                TimeUnit.MILLISECONDS, 
                new ArrayBlockingQueue<>(1000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.DiscardOldestPolicy()
        );
        while (true) {
            Socket accept = serverSocket.accept();
//            new ServiceThread(accept).start();
//            executorService.execute(new ServiceThread(accept));
            executorService.execute(new ThreadRunable(accept));

        }

//        read.close();
//        accept.close();

    }
}
