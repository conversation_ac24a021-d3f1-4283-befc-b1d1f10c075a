package com.fmb.manytcp;

import java.io.DataOutputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Scanner;

public class tcp {
    public static void main(String[] args) throws Exception {
        Socket socket = new Socket("127.0.0.1", 9999);
        OutputStream outputStream = socket.getOutputStream();

        DataOutputStream dataOutputStream = new DataOutputStream(outputStream);
        Scanner scanner = new Scanner(System.in);

        while (true) {
            String msg = scanner.next();
            if("exit".equals(msg)){
                dataOutputStream.writeUTF("exit");
                dataOutputStream.flush();
                socket.close();
                dataOutputStream.close();
                outputStream.close();
                System.out.println("发送完成");
                break;
            }
            dataOutputStream.writeUTF(msg);
//            dataOutputStream.writeInt(18);
//            dataOutputStream.writeBoolean(true);
            dataOutputStream.flush();
//        socket.close();
//        dataOutputStream.close();
//        outputStream.close();
            System.out.println("发送完成");
        }
    }
}
