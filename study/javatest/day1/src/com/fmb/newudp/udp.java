package com.fmb.newudp;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.Scanner;

public class udp {
    public static void main(String[] args) throws Exception {
        System.out.println("客户端启动");
        DatagramSocket socket = new DatagramSocket();

        Scanner sc = new Scanner(System.in);

        while (true) {
            String msg = sc.next();
            if("exit".equals(msg)){
                byte[] bytes = msg.getBytes();
                DatagramPacket packet = new DatagramPacket(bytes,bytes.length, InetAddress.getLocalHost(),8080);
                socket.send(packet);
                socket.close();
                break;
            }
//            String str = "hello world";
            byte[] bytes = msg.getBytes();
            DatagramPacket packet = new DatagramPacket(bytes,bytes.length, InetAddress.getLocalHost(),8080);

            socket.send(packet);
        }
    }
}
