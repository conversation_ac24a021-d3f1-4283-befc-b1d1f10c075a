package com.fmb.newudp;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.SocketException;

public class Udpservice {
    public static void main(String[] args) throws  Exception{
        try {
            System.out.println("服务端启动");
            DatagramSocket socket = new DatagramSocket(8080);
            byte[] bytes = new byte[1024];
            DatagramPacket packet = new DatagramPacket(bytes,bytes.length);
            while (true) {
                socket.receive(packet);
                String str = new String(packet.getData(),0,packet.getLength());
//            String str = new String(packet.getData(),0,packet.getLength());
//            String str2 = new String(bytes);
                String ip = packet.getAddress().getHostAddress();
                int port = packet.getPort();
                System.out.println(ip);
                System.out.println(str);
                System.out.println(port);

            }
        } catch (SocketException e) {


        }
    }
}
