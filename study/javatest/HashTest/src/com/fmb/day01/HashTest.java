package com.fmb.day01;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class HashTest {
    public static void main(String[] args) {
        Set<String> movies = new HashSet<>();
        movies.add("nazha");
        movies.add("fengshenbang");
        movies.add("xinxin");
        movies.add("xinxin111");
        movies.add("xinxin222");

        System.out.println(movies);

        Set<Double> movies2_score = new HashSet<>();
        movies2_score.add(9.5);
        movies2_score.add(6.5);
        movies2_score.add(7.5);
        System.out.println(movies2_score);

        Map<String, Double> movies2 = new HashMap<>();
        movies2.put("nazha", 9.5);
        movies2.put("fengshenbang", 6.5);
        movies2.put("xinxin", 7.5);
        Set<Map.Entry<String, Double>> movie = movies2.entrySet();
        for(Map.Entry<String, Double> m:movie){
            System.out.println(m.getKey()+"--"+m.getValue());
        }

        movie.forEach(m->System.out.println(m.getKey()+"--"+m.getValue()));


    }
}
